Author: <PERSON> sa<PERSON><EMAIL>
Description: Failed logon
EventId: 4625
Channel: Security
Maps: 
  - 
    Property: Username
    PropertyValue: "%domain%\\%user%"
    Values: 
      - 
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      - 
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  - 
    Property: RemoteHost
    PropertyValue: "%workstation% (%ipAddress%)"
    Values: 
      - 
        Name: ipAddress
        Value: "/Event/EventData/Data[@Name=\"IpAddress\"]"
      - 
        Name: workstation
        Value: "/Event/EventData/Data[@Name=\"WorkstationName\"]"
  - 
    Property: PayloadData1
    PropertyValue: "Target: %TargetDomainName%\\%TargetUserName%"
    Values: 
      - 
        Name: TargetDomainName
        Value: "/Event/EventData/Data[@Name=\"TargetDomainName\"]"
      - 
        Name: TargetUserName
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
  - 
    Property: PayloadData2
    PropertyValue: LogonType %LogonType%
    Values: 
      - 
        Name: LogonType
        Value: "/Event/EventData/Data[@Name=\"LogonType\"]"
  - 
    Property: ExecutableInfo
    PropertyValue: "%ProcessName%"
    Values: 
      - 
        Name: ProcessName
        Value: "/Event/EventData/Data[@Name=\"ProcessName\"]"
		

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
#   <EventData>
#     <Data Name="SubjectUserSid">S-1-5-18</Data>
#     <Data Name="SubjectUserName">BASE-RD-01$</Data>
#     <Data Name="SubjectDomainName">shieldbase</Data>
#     <Data Name="SubjectLogonId">0x3E7</Data>
#     <Data Name="TargetUserSid">S-1-0-0</Data>
#     <Data Name="TargetUserName">-</Data>
#     <Data Name="TargetDomainName">-</Data>
#     <Data Name="Status">0xC0000073</Data>
#     <Data Name="FailureReason">%%2304</Data>
#     <Data Name="SubStatus">0xC0000073</Data>
#     <Data Name="LogonType">5</Data>
#     <Data Name="LogonProcessName">Advapi  </Data>
#     <Data Name="AuthenticationPackageName">Negotiate</Data>
#     <Data Name="WorkstationName">-</Data>
#     <Data Name="TransmittedServices">-</Data>
#     <Data Name="LmPackageName">-</Data>
#     <Data Name="KeyLength">0</Data>
#     <Data Name="ProcessId">0x3E8</Data>
#     <Data Name="ProcessName">C:\Windows\System32\svchost.exe</Data>
#     <Data Name="IpAddress">-</Data>
#     <Data Name="IpPort">-</Data>
#   </EventData>