Author: <PERSON> sa<PERSON><PERSON><EMAIL>
Description: Scheduled task created
EventId: 106
Channel: "Microsoft-Windows-TaskScheduler/Operational"
Maps: 
  - 
    Property: PayloadData1
    PropertyValue: Task %TaskName%
    Values: 
      - 
        Name: TaskName
        Value: "/Event/EventData/Data[@Name=\"TaskName\"]"
  - 
    Property: UserName
    PropertyValue: "%UserContext%"
    Values: 
      - 
        Name: UserContext
        Value: "/Event/EventData/Data[@Name=\"UserContext\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-TaskScheduler" Guid="de7b24ea-73c8-4a09-985d-5bdadcfa9017" />
#     <EventID>106</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>106</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000000000000000</Keywords>
#     <TimeCreated SystemTime="2018-08-28 21:43:58.3567731" />
#     <EventRecordID>196639</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="1484" ThreadID="11844" />
#     <Channel>Microsoft-Windows-TaskScheduler/Operational</Channel>
#     <Computer>base-rd-01.shieldbase.lan</Computer>
#     <Security UserID="S-1-5-18" />
#   </System>
#   <EventData Name="TaskRegisteredEvent">
#     <Data Name="TaskName">\OneDrive Standalone Update Task-S-1-5-21-**********-**********-**********-1193</Data>
#     <Data Name="UserContext">shieldbase\spsql</Data>
#   </EventData>
# </Event>