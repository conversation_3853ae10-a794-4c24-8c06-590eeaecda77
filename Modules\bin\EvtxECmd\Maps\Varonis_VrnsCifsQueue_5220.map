Author: <PERSON>
Description: List of Devices Monitored and Logged by <PERSON><PERSON><PERSON>
EventId: 5220
Channel: Varonis
Provider: "VrnsCifsQueue"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%Data%"
    Values:
      -
        Name: Data
        Value: "/Event/EventData/Data"

# Documentation:
# There is no public documentation on these events. Varonis is a data security platform so some file system activity appears to be tracked by it.
# In this case, there were multiple events in a row that continued this list of devices monitored.
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="VrnsCifsQueue" />
#    <EventID Qualifiers="0">5220</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000003000000</Keywords>
#    <TimeCreated SystemTime="2020-08-13 07:21:08.0000000" />
#    <EventRecordID>307679</EventRecordID>
#    <Channel>Varonis</Channel>
#    <Computer>HOSTNAME.domain.com</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>VrnsCifsQueue, 4384, 2676, Device list:
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy12
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy13
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy14
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy15
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy16
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy17
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy18
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy19
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy20
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy21
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy22
#      [Logging]  [Monitored]  E:, \device\harddiskvolumeshadowcopy23
# &amp;lt;cont. next event...&amp;gt;</Data>
#    <Binary></Binary>
#  </EventData>
# </Event>
