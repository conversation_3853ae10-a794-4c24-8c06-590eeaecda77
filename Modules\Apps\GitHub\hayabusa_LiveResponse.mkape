Description: hayabusa live analysis
Category: EventLogs
Author: <PERSON>
Version: 1.0
Id: fedb5c4c-de8d-40e2-ad7c-3b590ccd5d4d
BinaryUrl: https://github.com/Yamato-Security/hayabusa
ExportFormat: csv
Processors:
    -
        Executable: hayabusa\hayabusa.exe
        CommandLine: --live-analysis --min-level low --utc --quiet --rfc-2822 -o %destinationDirectory%\hayabusa.csv
        ExportFormat: csv

# Documentation
# Create a folder "hayabusa" within the "Modules\bin" KAPE folder
# Place "zip archive" file into "Modules\bin\hayabusa" and unpack
# You can delete all except: "config"; "rules" and the "hayabusa.exe"
