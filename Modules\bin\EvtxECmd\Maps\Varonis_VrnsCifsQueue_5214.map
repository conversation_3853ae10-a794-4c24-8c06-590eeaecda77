Author: <PERSON>
Description: Volume Shadow Copy successfully mounted
EventId: 5214
Channel: Varonis
Provider: "VrnsCifsQueue"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%Data%"
    Values:
      -
        Name: Data
        Value: "/Event/EventData/Data"

# Documentation:
# There is no public documentation on these events. Varonis is a data security platform so some file system activity appears to be tracked by it.
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="VrnsCifsQueue" />
#    <EventID Qualifiers="0">5214</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000030000000</Keywords>
#    <TimeCreated SystemTime="2020-08-13 07:18:22.0000000" />
#    <EventRecordID>307679</EventRecordID>
#    <Channel>Varonis</Channel>
#    <Computer>HOSTNAME.domain.com</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>VrnsCifsQueue, 1234, 5678, <PERSON><PERSON><PERSON> attached to device \device\harddiskvolumeshadowcopy679, mounted on F:</Data>
#    <Binary></Binary>
#  </EventData>
# </Event>
