Author: <PERSON>
Description: RegistryEvent (Value Set)
EventId: 13
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: PayloadData1
    PropertyValue: "ProcessID: %ProcessID%, ProcessGUID: %ProcessGUID%"
    Values:
      -
        Name: ProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ProcessGuid\"]"
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "Image: %Image%"
    Values:
      -
        Name: Image
        Value: "/Event/EventData/Data[@Name=\"Image\"]"
  -
    Property: PayloadData4
    PropertyValue: "EventType: %EventType%"
    Values:
      -
        Name: EventType
        Value: "/Event/EventData/Data[@Name=\"EventType\"]"
  -
    Property: PayloadData5
    PropertyValue: "TargetObject: %TargetObject%"
    Values:
      -
        Name: TargetObject
        Value: "/Event/EventData/Data[@Name=\"TargetObject\"]"
  -
    Property: PayloadData6
    PropertyValue: "Details: %Details%"
    Values:
      -
        Name: Details
        Value: "/Event/EventData/Data[@Name=\"Details\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
# https://jpcertcc.github.io/ToolAnalysisResultSheet
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>13</EventID>
#    <Version>2</Version>
#    <Level>4</Level>
#    <Task>13</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-05-15 04:18:40.4746443" />
#    <EventRecordID>17915</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="2024" ThreadID="1212" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>IEWIN7</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName">Defense Evasion - access to the VBA project object model in the Macro Settings changed</Data>
#    <Data Name="EventType">SetValue</Data>
#    <Data Name="UtcTime">2019-05-15 04:18:40.459</Data>
#    <Data Name="ProcessGuid">365abb72-92df-5cdb-0000-0010a15e1300</Data>
#    <Data Name="ProcessId">3804</Data>
#    <Data Name="Image">C:\Windows\system32\wbem\wmiprvse.exe</Data>
#    <Data Name="TargetObject">HKU\S-1-5-21-3583694148-1414552638-2922671848-1000\Software\Microsoft\Office\16.0\Excel\Security\AccessVBOM</Data>
#    <Data Name="Details">DWORD (0x00000001)</Data>
#  </EventData>
# </Event>
