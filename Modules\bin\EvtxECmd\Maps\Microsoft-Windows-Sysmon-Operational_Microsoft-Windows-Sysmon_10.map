Author: <PERSON>
Description: ProcessAccess
EventId: 10
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "CallTrace: %CallTrace%"
    Values:
      -
        Name: CallTrace
        Value: "/Event/EventData/Data[@Name=\"CallTrace\"]"
  -
    Property: PayloadData2
    PropertyValue: "GrantedAccess: %GrantedAccess%"
    Values:
      -
        Name: GrantedAccess
        Value: "/Event/EventData/Data[@Name=\"GrantedAccess\"]"
  -
    Property: PayloadData3
    PropertyValue: "SourceProcessID: %SourceProcessID%, SourceProcessGUID: %SourceProcessGUID%"
    Values:
      -
        Name: SourceProcessGUID
        Value: "/Event/EventData/Data[@Name=\"SourceProcessGuid\"]"
      -
        Name: SourceProcessID
        Value: "/Event/EventData/Data[@Name=\"SourceProcessId\"]"
  -
    Property: PayloadData4
    PropertyValue: "SourceImage: %SourceImage%"
    Values:
      -
        Name: SourceImage
        Value: "/Event/EventData/Data[@Name=\"SourceImage\"]"

  -
    Property: PayloadData5
    PropertyValue: "TargetProcessID: %TargetProcessID%, TargetProcessGUID: %TargetProcessGUID%"
    Values:
      -
        Name: TargetProcessGUID
        Value: "/Event/EventData/Data[@Name=\"TargetProcessGuid\"]"
      -
        Name: TargetProcessID
        Value: "/Event/EventData/Data[@Name=\"TargetProcessId\"]"
  -
    Property: PayloadData6
    PropertyValue: "TargetImage: %TargetImage%"
    Values:
      -
        Name: TargetImage
        Value: "/Event/EventData/Data[@Name=\"TargetImage\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>10</EventID>
#    <Version>3</Version>
#    <Level>4</Level>
#    <Task>10</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-04-30 12:43:43.7841796" />
#    <EventRecordID>9058</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="1964" ThreadID="316" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>IEWIN7</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName"></Data>
#    <Data Name="UtcTime">2019-04-30 12:43:43.784</Data>
#    <Data Name="SourceProcessGUID">365abb72-4055-5cc8-0000-0010769d0b00</Data>
#    <Data Name="SourceProcessId">1532</Data>
#    <Data Name="SourceThreadId">2284</Data>
#    <Data Name="SourceImage">\\VBOXSVR\HTools\voice_mail.msg.exe</Data>
#    <Data Name="TargetProcessGUID">365abb72-3fde-5cc8-0000-0010142b0000</Data>
#    <Data Name="TargetProcessId">264</Data>
#    <Data Name="TargetImage">C:\Windows\System32\smss.exe</Data>
#    <Data Name="GrantedAccess">0x1F1FFF</Data>
#    <Data Name="CallTrace">C:\Windows\SYSTEM32\ntdll.dll+4595c|C:\Windows\system32\KERNELBASE.dll+8185|UNKNOWN(01B51606)|UNKNOWN(01B51F41)|UNKNOWN(01B51515)|UNKNOWN(014A7486)|UNKNOWN(014A73A0)|UNKNOWN(014A78A3)|C:\Windows\system32\kernel32.dll+4ef8c|C:\Windows\SYSTEM32\ntdll.dll+6367a|C:\Windows\SYSTEM32\ntdll.dll+6364d</Data>
#  </EventData>
# </Event>
