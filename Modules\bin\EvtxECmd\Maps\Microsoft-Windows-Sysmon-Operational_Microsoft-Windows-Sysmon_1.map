Author: <PERSON>
Description: Process creation
EventId: 1
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:

  -
    Property: ExecutableInfo
    PropertyValue: "%CommandLine%"
    Values:
      -
        Name: CommandLine
        Value: "/Event/EventData/Data[@Name=\"CommandLine\"]"
  -
    Property: PayloadData1
    PropertyValue: "ProcessID: %ProcessID%, ProcessGUID: %ProcessGUID%"
    Values:
      -
        Name: ProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ProcessGuid\"]"
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "%Hashes%"
    Values:
      -
        Name: Hashes
        Value: "/Event/EventData/Data[@Name=\"Hashes\"]"
  -
    Property: PayloadData4
    PropertyValue: "ParentProcess: %ParentProcess%"
    Values:
      -
        Name: ParentProcess
        Value: "/Event/EventData/Data[@Name=\"ParentImage\"]"
  -
    Property: PayloadData5
    PropertyValue: "ParentProcessID: %ParentProcessID%, ParentProcessGUID: %ParentProcessGUID%"
    Values:
      -
        Name: ParentProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ParentProcessGuid\"]"
      -
        Name: ParentProcessID
        Value: "/Event/EventData/Data[@Name=\"ParentProcessId\"]"
  -
    Property: PayloadData6
    PropertyValue: "ParentCommandLine: %ParentCommandLine%"
    Values:
      -
        Name: ParentCommandLine
        Value: "/Event/EventData/Data[@Name=\"ParentCommandLine\"]"
  -
    Property: UserName
    PropertyValue: "%User%"
    Values:
      -
        Name: User
        Value: "/Event/EventData/Data[@Name=\"User\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
# https://jpcertcc.github.io/ToolAnalysisResultSheet
#
# Example Event Data:
# <Event>
# <System>
#  <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#  <EventID>1</EventID>
#  <Version>5</Version>
#  <Level>4</Level>
#  <Task>1</Task>
#  <Opcode>0</Opcode>
#  <Keywords>0x8000000000000000</Keywords>
#  <TimeCreated SystemTime="2019-05-26 04:01:42.3855056" />
#  <EventRecordID>4857</EventRecordID>
#  <Correlation />
#  <Execution ProcessID="984" ThreadID="2352" />
#  <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#  <Computer>IEWIN7</Computer>
#  <Security UserID="S-1-5-18" />
# </System>
# <EventData>
#  <Data Name="RuleName"></Data>
#  <Data Name="UtcTime">2019-05-26 04:01:42.375</Data>
#  <Data Name="ProcessGuid">365abb72-0fa6-5cea-0000-001049b50a00</Data>
#  <Data Name="ProcessId">3836</Data>
#  <Data Name="Image">C:\Users\<USER>\Desktop\info.rar\jjs.exe</Data>
#  <Data Name="FileVersion">8.0.1710.11</Data>
#  <Data Name="Description">Java(TM) Platform SE binary</Data>
#  <Data Name="Product">Java(TM) Platform SE 8</Data>
#  <Data Name="Company">Oracle Corporation</Data>
#  <Data Name="CommandLine">"C:\Users\<USER>\Desktop\info.rar\jjs.exe" </Data>
#  <Data Name="CurrentDirectory">C:\Users\<USER>\Desktop\info.rar\</Data>
#  <Data Name="User">IEWIN7\IEUser</Data>
#  <Data Name="LogonGuid">365abb72-0f31-5cea-0000-002062290100</Data>
#  <Data Name="LogonId">0x12962</Data>
#  <Data Name="TerminalSessionId">1</Data>
#  <Data Name="IntegrityLevel">High</Data>
#  <Data Name="Hashes">SHA1=8CC66ED54FBEFF205151898D65F6415400124553,MD5=64FDBD98584331982A15B1F2DF7F08DA,SHA256=B5DE10A0091B7AAF491BDB810BCE6DAB3F6B4A1C7A917722B5DE014E4A08B6EB,IMPHASH=D3310CE6CBCACB3A9F0809BC33E38ABE</Data>
#  <Data Name="ParentProcessGuid">365abb72-0f32-5cea-0000-0010b5460100</Data>
#  <Data Name="ParentProcessId">1372</Data>
#  <Data Name="ParentImage">C:\Windows\explorer.exe</Data>
#  <Data Name="ParentCommandLine">C:\Windows\Explorer.EXE</Data>
# </EventData>
