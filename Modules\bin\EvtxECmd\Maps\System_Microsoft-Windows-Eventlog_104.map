Author: <PERSON><PERSON> @gazambelli
Description: Event log cleared
EventId: 104
Channel: System
Provider: Microsoft-Windows-Eventlog
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/UserData/LogFileCleared/SubjectDomainName"
      -
        Name: user
        Value: "/Event/UserData/LogFileCleared/SubjectUserName"
  -
    Property: PayloadData1
    PropertyValue: The %Channel% log file was cleared
    Values:
      -
        Name: Channel
        Value: "/Event/UserData/LogFileCleared/Channel"

# Documentation:
# https://twitter.com/matthewdunwoody/status/832311474508931072?lang=en
# https://kb.eventtracker.com/evtpass/evtpages/EventId_104_Microsoft-Windows-Eventlog_64337.asp
# https://community.spiceworks.com/topic/126652-event-viewer-id-104
# https://www.techrepublic.com/forums/discussions/event-viewer-id-104/
#
# Example Event Data:
# <Event>
# <System>
# <Provider Name="Microsoft-Windows-Eventlog" Guid="{fc65ddd8-d6ef-4962-83d5-6e5cfe9ce148}" />
# <EventID>104</EventID>
# <Version>0</Version>
# <Level>4</Level>
# <Task>104</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8000000000000000</Keywords>
# <TimeCreated SystemTime="2020-11-27 18:43:29.7273880" />
# <EventRecordID>5170</EventRecordID>
# <Correlation />
# <Execution ProcessID="788" ThreadID="2200" />
# <Channel>System</Channel>
# <Computer>IEWIN7</Computer>
# <Security UserID="S-1-5-21-**********-122519599-941061767-1000" />
# </System>
# <UserData>
# <LogFileCleared>
# <SubjectUserName>IEUser</SubjectUserName>
# <SubjectDomainName>IEWIN7</SubjectDomainName>
# <Channel>System</Channel>
# <BackupPath></BackupPath>
# </LogFileCleared>
# </UserData>
# </Event>
