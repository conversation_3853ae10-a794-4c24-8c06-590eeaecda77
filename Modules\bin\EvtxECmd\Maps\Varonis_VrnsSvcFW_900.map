Author: <PERSON>
Description: Volume information
EventId: 900
Channel: Varonis
Provider: "VrnsSvcFW"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%Data%"
    Values:
      -
        Name: Data
        Value: "/Event/EventData/Data"

# Documentation:
# There is no public documentation on these events. Varonis is a data security platform so some file system activity appears to be tracked by it.
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="VrnsSvcFW" />
#    <EventID Qualifiers="0">900</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000070000000</Keywords>
#    <TimeCreated SystemTime="2020-09-15 21:45:52.0000000" />
#    <EventRecordID>679</EventRecordID>
#    <Channel>Varonis</Channel>
#    <Computer>HOSTNAME.domain.com</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>VrnsSvcFW, 1234, 5678, Volume info saved. Path: \\?\C:\ Type: 467913 Serial: 0xae70mom570maa42f
# </Data>
#    <Binary></Binary>
#  </EventData>
# </Event>
#
# I've also seen the following logged here:
# <EventData>
#    <Data>VrnsSvcFW, 1234, 5678, 3, CDirFileWalk::QueryEntryInfo() Failed FindFirstFile() [path: \\?\C:\Windows\System32\config\systemprofile\AppData\Local\Microsoft\Windows\INetCache\Content.IE5]
#
# Error message description: The system cannot find the path specified.</Data>
