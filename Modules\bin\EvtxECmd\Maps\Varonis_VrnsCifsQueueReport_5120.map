Author: <PERSON>
Description: Volumne statistics
EventId: 5120
Channel: <PERSON>aronis
Provider: "VrnsCifsQueueReport"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%Data%"
    Values:
      -
        Name: Data
        Value: "/Event/EventData/Data"

# Documentation:
# There is no public documentation on these events. Varonis is a data security platform so some file system activity appears to be tracked by it.
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="VrnsCifsQueueReport" />
#    <EventID Qualifiers="0">5120</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000045000000</Keywords>
#    <TimeCreated SystemTime="2020-08-13 08:11:14.0000000" />
#    <EventRecordID>306793</EventRecordID>
#    <Channel>Varonis</Channel>
#    <Computer>HOSTNAME.domain.com</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>VrnsCifsQueue, 4343, 4545, [HOSTNAME:55:ProxyLocal]:	CONNECTED
#    [First connection]:	8/22/2020 3:17:54 AM;  0d 00:53:20 ago
#    [Last connection]:	8/22/2020 4:11:09 AM;  0d 00:00:05 ago
#    [Statistics]:
#        [Events Lost (queue)]: 	0         	of total:	0
#        [Events Sent To Probe]:	0         	of total:	0
#    [Volumes]:
# E:	==&amp;gt;
# 	E:, \device\harddiskvolumeshadowcopy31
# 	E:, \device\harddiskvolumeshadowcopy32
# 	E:, \device\harddiskvolumeshadowcopy33
# 	E:, \device\harddiskvolumeshadowcopy65
# 	E:, \device\harddiskvolumeshadowcopy66
# 	E:, \device\harddiskvolumeshadowcopy67
# 	E:, \device\harddiskvolumeshadowcopy60
# 	E:, \device\harddiskvolumeshadowcopy6
# 	E:, \device\harddiskvolumeshadowcopy58
# 	E:, \device\harddiskvolumeshadowcopy58
# 	E:, \device\harddiskvolumeshadowcopy53
# 	E:, \device\harddiskvolumeshadowcopy56
# &amp;lt;cont. next event...&amp;gt;</Data>
#    <Binary></Binary>
#  </EventData>
# </Event>
# <Event>
