Author: <PERSON>
Description: CreateRemoteThread
EventId: 8
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: PayloadData1
    PropertyValue: "StartAddress: %StartAddress%"
    Values:
      -
        Name: StartAddress
        Value: "/Event/EventData/Data[@Name=\"StartAddress\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "SourceProcessID: %SourceProcessID%, SourceProcessGUID: %SourceProcessGUID%"
    Values:
      -
        Name: SourceProcessGUID
        Value: "/Event/EventData/Data[@Name=\"SourceProcessGuid\"]"
      -
        Name: SourceProcessID
        Value: "/Event/EventData/Data[@Name=\"SourceProcessId\"]"
  -
    Property: PayloadData4
    PropertyValue: "SourceImage: %SourceImage%"
    Values:
      -
        Name: SourceImage
        Value: "/Event/EventData/Data[@Name=\"SourceImage\"]"
  -
    Property: PayloadData5
    PropertyValue: "TargetProcessID: %TargetProcessID%, TargetProcessGUID: %TargetProcessGUID%"
    Values:
      -
        Name: TargetProcessGUID
        Value: "/Event/EventData/Data[@Name=\"TargetProcessGuid\"]"
      -
        Name: TargetProcessID
        Value: "/Event/EventData/Data[@Name=\"TargetProcessId\"]"
  -
    Property: PayloadData6
    PropertyValue: "TargetImage: %TargetImage%"
    Values:
      -
        Name: TargetImage
        Value: "/Event/EventData/Data[@Name=\"TargetImage\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>8</EventID>
#    <Version>2</Version>
#    <Level>4</Level>
#    <Task>8</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-04-27 18:47:00.0624748" />
#    <EventRecordID>7023</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="1816" ThreadID="1228" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>IEWIN7</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName"></Data>
#    <Data Name="UtcTime">2019-04-27 18:47:00.062</Data>
#    <Data Name="SourceProcessGuid">365abb72-a3a4-5cc4-0000-001084960c00</Data>
#    <Data Name="SourceProcessId">1288</Data>
#    <Data Name="SourceImage">C:\Users\<USER>\KeeFarce.exe</Data>
#    <Data Name="TargetProcessGuid">365abb72-a201-5cc4-0000-00104f500800</Data>
#    <Data Name="TargetProcessId">2364</Data>
#    <Data Name="TargetImage">C:\Program Files\KeePass Password Safe 2\KeePass.exe</Data>
#    <Data Name="NewThreadId">1920</Data>
#    <Data Name="StartAddress">0x5A801260</Data>
#    <Data Name="StartModule">C:\Users\<USER>\BootstrapDLL.dll</Data>
#    <Data Name="StartFunction">LoadManagedProject</Data>
#  </EventData>
# </Event>
