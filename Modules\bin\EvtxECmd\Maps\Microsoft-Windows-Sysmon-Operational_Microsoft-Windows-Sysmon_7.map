Author: <PERSON>
Description: Image loaded
EventId: 7
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "%ImageLoaded%"
    Values:
      -
        Name: ImageLoaded
        Value: "/Event/EventData/Data[@Name=\"ImageLoaded\"]"
  -
    Property: PayloadData1
    PropertyValue: "ProcessID: %ProcessID%, ProcessGUID: %ProcessGUID%"
    Values:
      -
        Name: ProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ProcessGuid\"]"
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "%Hashes%"
    Values:
      -
        Name: Hashes
        Value: "/Event/EventData/Data[@Name=\"Hashes\"]"
  -
    Property: PayloadData4
    PropertyValue: "Signed: %Signed%"
    Values:
      -
        Name: Signed
        Value: "/Event/EventData/Data[@Name=\"Signed\"]"
  -
    Property: PayloadData5
    PropertyValue: "Image: %Image%"
    Values:
      -
        Name: Image
        Value: "/Event/EventData/Data[@Name=\"Image\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>7</EventID>
#    <Version>3</Version>
#    <Level>4</Level>
#    <Task>7</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-04-18 17:01:35.6807168" />
#    <EventRecordID>29</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="3192" ThreadID="3288" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>IEWIN7</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName">technique_id=T1003,technique_name=Credential Dumping</Data>
#    <Data Name="UtcTime">2019-04-18 17:01:35.680</Data>
#    <Data Name="ProcessGuid">365abb72-ac28-5cb8-0000-0010f3f70700</Data>
#    <Data Name="ProcessId">1200</Data>
#    <Data Name="Image">C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe</Data>
#    <Data Name="ImageLoaded">C:\Windows\System32\vaultcli.dll</Data>
#    <Data Name="FileVersion">6.1.7600.16385 (win7_rtm.090713-1255)</Data>
#    <Data Name="Description">Credential Vault Client Library</Data>
#    <Data Name="Product">Microsoft Windows Operating System</Data>
#    <Data Name="Company">Microsoft Corporation</Data>
#    <Data Name="Hashes">SHA1=9A398500E906FA979C21CD9F19C929FE798AF9EF,MD5=36B8D5903CEEF0AA42A1EE002BD27FF1,SHA256=CBD5C4D0E05B9A2657D816B655FFFC386807061594DEAABA754658D3152F7403,IMPHASH=55954B415EBB6BF5B592831A5E07DC56</Data>
#    <Data Name="Signed">true</Data>
#    <Data Name="Signature">Microsoft Windows</Data>
#    <Data Name="SignatureStatus">Valid</Data>
#  </EventData>
# </Event>
