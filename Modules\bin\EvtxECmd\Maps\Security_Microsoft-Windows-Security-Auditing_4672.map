Author: <PERSON> sa<PERSON><EMAIL>
Description: Administrative logon
EventId: 4672
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user% (%sid%)"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"SubjectUserSid\"]"
  -
    Property: PayloadData1
    PropertyValue: "PrivilegeList: %PrivilegeList%"
    Values:
      -
        Name: PrivilegeList
        Value: "/Event/EventData/Data[@Name=\"PrivilegeList\"]"
  -
    Property: PayloadData2
    PropertyValue: "LogonId: %SubjectLogonId%"
    Values:
      -
        Name: SubjectLogonId
        Value: "/Event/EventData/Data[@Name=\"SubjectLogonId\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4672
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4672
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-A5BA-3E3B0328C30D}" />
# <EventID>4672</EventID>
# <Version>0</Version>
# <Level>0</Level>
# <Task>12548</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8020000000000000</Keywords>
# <TimeCreated SystemTime="2015-09-11T01:10:57.091809600Z" />
# <EventRecordID>237692</EventRecordID>
# <Correlation />
# <Execution ProcessID="504" ThreadID="524" />
# <Channel>Security</Channel>
# <Computer>DC01.contoso.local</Computer>
# <Security />
# </System>
#  <EventData>
# <Data Name="SubjectUserSid">S-1-5-21-**********-**********-823803824-1104</Data>
# <Data Name="SubjectUserName">dadmin</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x671101</Data>
# <Data Name="PrivilegeList">SeTcbPrivilege SeSecurityPrivilege SeTakeOwnershipPrivilege SeLoadDriverPrivilege SeBackupPrivilege SeRestorePrivilege SeDebugPrivilege SeSystemEnvironmentPrivilege SeEnableDelegationPrivilege SeImpersonatePrivilege</Data>
# </EventData>
# </Event>
