Author: <PERSON><PERSON> <PERSON> @hyuunnn
Description: The firewall has enabled/disabled.
EventId: 2003
Channel: Microsoft-Windows-Windows Firewall With Advanced Security/Firewall
Provider: Microsoft-Windows-Windows Firewall With Advanced Security
Maps:
  -
    Property: PayloadData1
    PropertyValue: "SettingValue: %SettingValue%"
    Values:
      -
        Name: SettingValue
        Value: "/Event/EventData/Data[@Name=\"SettingValue\"]"
Lookups:
  -
    Name: SettingValue
    Default: Unknown code
    Values:
      01-00-00-00: Enable
      00-00-00-00: Disable

# Documentation:
# https://kb.eventtracker.com/evtpass/evtPages/EventId_2003_Microsoft-Windows-WindowsFirewallwithAdvancedS_65672.asp
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#     <Provider Name="Microsoft-Windows-Windows Firewall With Advanced Security" Guid="{GUID}" />
#     <EventID>2003</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>0</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000000000000000</Keywords>
#     <TimeCreated SystemTime="2020-12-29T08:02:40.1537755Z" />
#     <EventRecordID>3921</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="2952" ThreadID="5308" />
#     <Channel>Microsoft-Windows-Windows Firewall With Advanced Security/Firewall</Channel>
#     <Computer>ComputerName</Computer>
#     <Security UserID="{UserID}" />
#   </System>
#   <EventData>
#     <Data Name="Profiles">2</Data>
#     <Data Name="SettingType">1</Data>
#     <Data Name="SettingValueSize">4</Data>
#     <Data Name="SettingValue">01000000</Data>
#     <Data Name="SettingValueString">Yes</Data>
#     <Data Name="Origin">1</Data>
#     <Data Name="ModifyingUser">S-1-5-21-513373498-**********-**********-1001</Data>
#     <Data Name="ModifyingApplication">C:\Windows\explorer.exe</Data>
#   </EventData>
# </Event>
