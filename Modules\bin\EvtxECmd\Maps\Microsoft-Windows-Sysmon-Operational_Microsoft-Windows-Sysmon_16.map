Author: <PERSON>
Description: ServiceConfigurationChange
EventId: 16
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "%Configuration%"
    Values:
      -
        Name: Configuration
        Value: "/Event/EventData/Data[@Name=\"Configuration\"]"
  -
    Property: PayloadData1
    PropertyValue: "ConfigurationFileHash: %ConfigurationFileHash%"
    Values:
      -
        Name: ConfigurationFileHash
        Value: "/Event/EventData/Data[@Name=\"ConfigurationFileHash\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>16</EventID>
#    <Version>3</Version>
#    <Level>4</Level>
#    <Task>16</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-04-18 16:55:37.0149808" />
#    <EventRecordID>1</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="2000" ThreadID="2548" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>IEWIN7</Computer>
#    <Security UserID="S-1-5-21-**********-**********-**********-1000" />
#  </System>
#  <EventData>
#    <Data Name="UtcTime">2019-04-18 16:55:37.014</Data>
#    <Data Name="Configuration">C:\Users\<USER>\Desktop\Sysmon.exe  -i</Data>
#    <Data Name="ConfigurationFileHash"></Data>
#  </EventData>
# </Event>
