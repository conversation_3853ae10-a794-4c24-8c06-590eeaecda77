Author: <PERSON>
Description: Handle requested to an object
EventId: 4661
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  -
    Property: PayloadData1
    PropertyValue: "ObjectServer: %ObjectServer%"
    Values:
      -
        Name: ObjectServer
        Value: "/Event/EventData/Data[@Name=\"ObjectServer\"]"
  -
    Property: PayloadData2
    PropertyValue: "ObjectType: %ObjectType%"
    Values:
      -
        Name: ObjectType
        Value: "/Event/EventData/Data[@Name=\"ObjectType\"]"
  -
    Property: PayloadData3
    PropertyValue: "ObjectName: %ObjectName%"
    Values:
      -
        Name: ObjectName
        Value: "/Event/EventData/Data[@Name=\"ObjectName\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%ProcessName%"
    Values:
      -
        Name: ProcessName
        Value: "/Event/EventData/Data[@Name=\"ProcessName\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4661
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4661
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-A5BA-3E3B0328C30D}" />
# <EventID>4661</EventID>
# <Version>0</Version>
# <Level>0</Level>
# <Task>14080</Task>
# <Opcode>0</Opcode>
# <Keywords>0x802************0</Keywords>
# <TimeCreated SystemTime="2015-09-30T00:11:56.547696700Z" />
# <EventRecordID>1048009</EventRecordID>
# <Correlation />
# <Execution ProcessID="520" ThreadID="528" />
# <Channel>Security</Channel>
# <Computer>DC01.contoso.local</Computer>
# <Security />
# </System>
# <EventData>
# <Data Name="SubjectUserSid">S-1-5-21-**********-**********-*********-1104</Data>
# <Data Name="SubjectUserName">dadmin</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x4280e</Data>
# <Data Name="ObjectServer">Security Account Manager</Data>
# <Data Name="ObjectType">SAM\_DOMAIN</Data>
# <Data Name="ObjectName">DC=contoso,DC=local</Data>
# <Data Name="HandleId">0xdd64d36870</Data>
# <Data Name="TransactionId">{********-0000-0000-0000-************}</Data>
# <Data Name="AccessList">%%5400</Data>
# <Data Name="AccessMask">0x2d</Data>
# <Data Name="PrivilegeList">Ā</Data>
# <Data Name="Properties">-</Data>
# <Data Name="RestrictedSidCount">2949165</Data>
# <Data Name="ProcessId">0x9000a000d002d</Data>
# <Data Name="ProcessName">{bf967a90-0de6-11d0-a285-00aa003049e2} %%5400 {ccc2dc7d-a6ad-4a7a-8846-c04e3cc53501}</Data>
# </EventData>
# </Event>
