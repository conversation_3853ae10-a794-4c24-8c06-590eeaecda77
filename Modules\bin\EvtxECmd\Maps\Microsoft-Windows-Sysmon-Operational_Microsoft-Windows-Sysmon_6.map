Author: <PERSON>
Description: Driver loaded
EventId: 6
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "%ImageLoaded%"
    Values:
      -
        Name: ImageLoaded
        Value: "/Event/EventData/Data[@Name=\"ImageLoaded\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "%Hashes%"
    Values:
      -
        Name: Hashes
        Value: "/Event/EventData/Data[@Name=\"Hashes\"]"
  -
    Property: PayloadData4
    PropertyValue: "Signed: %Signed%"
    Values:
      -
        Name: Signed
        Value: "/Event/EventData/Data[@Name=\"Signed\"]"
  -
    Property: PayloadData5
    PropertyValue: "Signature: %Signature%"
    Values:
      -
        Name: Signature
        Value: "/Event/EventData/Data[@Name=\"Signature\"]"
  -
    Property: PayloadData6
    PropertyValue: "SignatureStatus: %SignatureStatus%"
    Values:
      -
        Name: SignatureStatus
        Value: "/Event/EventData/Data[@Name=\"SignatureStatus\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#       <Provider Name="Microsoft-Windows-Sysmon" Guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}" />
#       <EventID>6</EventID>
#       <Version>3</Version>
#       <Level>4</Level>
#       <Task>6</Task>
#       <Opcode>0</Opcode>
#       <Keywords>0x8000000000000000</Keywords>
#       <TimeCreated SystemTime="2017-04-28T21:33:47.350855600Z" />
#       <EventRecordID>2864</EventRecordID>
#       <Correlation />
#       <Execution ProcessID="3216" ThreadID="3980" />
#       <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#       <Computer>rfsH.lab.local</Computer>
#       <Security UserID="S-1-5-18" />
#   </System>
#   <EventData>
#       <Data Name="UtcTime">2017-04-28 21:33:47.345</Data>
#       <Data Name="ImageLoaded">C:\Windows\System32\drivers\usbscan.sys</Data>
#       <Data Name="Hashes">SHA256=D97DB59C9CAE2B8B33C707E8CEA7A65BF88712842CC715D270F7432A99D21BB6</Data>
#       <Data Name="Signed">true</Data>
#       <Data Name="Signature">Microsoft Windows</Data>
#       <Data Name="SignatureStatus">Valid</Data>
#   </EventData>
# </Event>
