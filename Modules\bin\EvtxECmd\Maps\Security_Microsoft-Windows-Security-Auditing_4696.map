Author: <PERSON>
Description: A privileged service was called
EventId: 4696
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user% (%sid%)"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"SubjectUserSid\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%ProcessName% (PID: %ProcessId%)"
    Values:
      -
        Name: ProcessName
        Value: "/Event/EventData/Data[@Name=\"ProcessName\"]"
      -
        Name: ProcessId
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData1
    PropertyValue: "Target: %domain%\\%user% (%sid%)"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"TargetDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"TargetUserSid\"]"
  -
    Property: PayloadData6
    PropertyValue: "%TargetProcessName% (PID: %TargetProcessId%)"
    Values:
      -
        Name: TargetProcessName
        Value: "/Event/EventData/Data[@Name=\"TargetProcessName\"]"
      -
        Name: TargetProcessId
        Value: "/Event/EventData/Data[@Name=\"TargetProcessId\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4696
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4696
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-a5ba-3e3b0328c30d}" />
# <EventID>4696</EventID>
# <Version>0</Version>
# <Level>0</Level>
# <Task>13312</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8020000000000000</Keywords>
# <TimeCreated SystemTime="2015-08-25T21:33:42.401Z" />
# <EventRecordID>561</EventRecordID>
# <Correlation />
# <Execution ProcessID="4" ThreadID="88" />
# <Channel>Security</Channel>
# <Computer>Win2008.contoso.local</Computer>
# <Security />
# </System>
# <EventData>
# <Data Name="SubjectUserSid">S-1-5-18</Data>
# <Data Name="SubjectUserName">WIN2008$</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x3e7</Data>
# <Data Name="TargetUserSid">S-1-5-18</Data>
# <Data Name="TargetUserName">dadmin</Data>
# <Data Name="TargetDomainName">CONTOSO</Data>
# <Data Name="TargetLogonId">0x1c8c5</Data>
# <Data Name="TargetProcessId">0xf40</Data>
# <Data Name="TargetProcessName">C:\\Windows\\System32\\WerFault.exe</Data>
# <Data Name="ProcessId">0x698</Data>
# <Data Name="ProcessName">C:\\Windows\\System32\\svchost.exe</Data>
# </EventData>
# </Event>
