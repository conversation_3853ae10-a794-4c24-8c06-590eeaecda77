Author: <PERSON> sa<PERSON><EMAIL>
Description: R<PERSON> disconnecting
EventId: 4779
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"AccountDomain\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"AccountName\"]"
  -
    Property: RemoteHost
    PropertyValue: "%ClientName% (%ClientAddress%)"
    Values:
      -
        Name: ClientAddress
        Value: "/Event/EventData/Data[@Name=\"ClientAddress\"]"
      -
        Name: ClientName
        Value: "/Event/EventData/Data[@Name=\"ClientName\"]"
  -
    Property: PayloadData1
    PropertyValue: "%SessionName%"
    Values:
      -
        Name: SessionName
        Value: "/Event/EventData/Data[@Name=\"SessionName\"]"
  -
    Property: PayloadData3
    PropertyValue: "LogonId: %LogonID%"
    Values:
      -
        Name: LogonID
        Value: "/Event/EventData/Data[@Name=\"LogonID\"]"

# Documentation:
# https://ponderthebits.com/2018/02/windows-rdp-related-event-logs-identification-tracking-and-investigation/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4779
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4779
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Security-Auditing" Guid="*************-4994-a5ba-3e3b0328c30d" />
#    <EventID>4779</EventID>
#    <Version>0</Version>
#    <Level>0</Level>
#    <Task>12551</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8020000000000000</Keywords>
#    <TimeCreated SystemTime="2018-07-17 19:06:57.1598375" />
#    <EventRecordID>25348</EventRecordID>
#    <Correlation ActivityID="3fd16c43-fc41-0001-ad6c-d13f41fcd301" />
#    <Execution ProcessID="744" ThreadID="6052" />
#    <Channel>Security</Channel>
#    <Computer>base-rd-01.shieldbase.lan</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="AccountName">tdungan</Data>
#    <Data Name="AccountDomain">shieldbase</Data>
#    <Data Name="LogonID">0x2A8EACE</Data>
#    <Data Name="SessionName">RDP-Tcp#5</Data>
#    <Data Name="ClientName">DUNGANATOR</Data>
#    <Data Name="ClientAddress">*************</Data>
#  </EventData>
# </Event>
