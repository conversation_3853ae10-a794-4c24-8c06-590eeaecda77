Author: <PERSON>
Description: A user account was changed
EventId: 4738
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  -
    Property: PayloadData1
    PropertyValue: "Target: %TargetDomainName%\\%TargetUserName%"
    Values:
      -
        Name: TargetDomainName
        Value: "/Event/EventData/Data[@Name=\"TargetDomainName\"]"
      -
        Name: TargetUserName
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
  -
    Property: PayloadData2
    PropertyValue: "Changed Attribute SamAccountName: %SamAccountName% DisplayName: %DisplayName% UserPrincipalName: %UserPrincipalName% HomeDirectory: %HomeDirectory% HomePath: %HomePath% ScriptPath: %ScriptPath% ProfilePath: %ProfilePath% UserWorkstations: %UserWorkstations% PasswordLastSet: %PasswordLastSet% AccountExpires: %AccountExpires% PrimaryGroupId: %PrimaryGroupId% AllowedToDelegateTo: %AllowedToDelegateTo% OldUacValue: %OldUacValue% NewUacValue: %NewUacValue% UserAccountControl: %UserAccountControl% UserParameters: %UserParameters% SidHistory: %SidHistory% LogonHours: %LogonHours%"
    Values:
      -
        Name: SamAccountName
        Value: "/Event/EventData/Data[@Name=\"SamAccountName\"]"
      -
        Name: DisplayName
        Value: "/Event/EventData/Data[@Name=\"DisplayName\"]"
      -
        Name: UserPrincipalName
        Value: "/Event/EventData/Data[@Name=\"UserPrincipalName\"]"
      -
        Name: HomeDirectory
        Value: "/Event/EventData/Data[@Name=\"HomeDirectory\"]"
      -
        Name: HomePath
        Value: "/Event/EventData/Data[@Name=\"HomePath\"]"
      -
        Name: ScriptPath
        Value: "/Event/EventData/Data[@Name=\"ScriptPath\"]"
      -
        Name: ProfilePath
        Value: "/Event/EventData/Data[@Name=\"ProfilePath\"]"
      -
        Name: UserWorkstations
        Value: "/Event/EventData/Data[@Name=\"UserWorkstations\"]"
      -
        Name: PasswordLastSet
        Value: "/Event/EventData/Data[@Name=\"PasswordLastSet\"]"
      -
        Name: AccountExpires
        Value: "/Event/EventData/Data[@Name=\"AccountExpires\"]"
      -
        Name: PrimaryGroupId
        Value: "/Event/EventData/Data[@Name=\"PrimaryGroupId\"]"
      -
        Name: AllowedToDelegateTo
        Value: "/Event/EventData/Data[@Name=\"AllowedToDelegateTo\"]"
      -
        Name: OldUacValue
        Value: "/Event/EventData/Data[@Name=\"OldUacValue\"]"
      -
        Name: NewUacValue
        Value: "/Event/EventData/Data[@Name=\"NewUacValue\"]"
      -
        Name: UserAccountControl
        Value: "/Event/EventData/Data[@Name=\"UserAccountControl\"]"
      -
        Name: UserParameters
        Value: "/Event/EventData/Data[@Name=\"UserParameters\"]"
      -
        Name: SidHistory
        Value: "/Event/EventData/Data[@Name=\"SidHistory\"]"
      -
        Name: LogonHours
        Value: "/Event/EventData/Data[@Name=\"LogonHours\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4738
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4738
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Security-Auditing" Guid="*************-4994-a5ba-3e3b0328c30d" />
#    <EventID>4738</EventID>
#    <Version>0</Version>
#    <Level>0</Level>
#    <Task>13824</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8020000090000000</Keywords>
#    <TimeCreated SystemTime="2020-10-02 22:18:59.4687500" />
#    <EventRecordID>***********</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="552" ThreadID="1928" />
#    <Channel>Security</Channel>
#    <Computer>HOSTNAME.domain.com</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="Dummy">-</Data>
#    <Data Name="TargetUserName">SM_f628653781234ab0a</Data>
#    <Data Name="TargetDomainName">DOMAIN</Data>
#    <Data Name="TargetSid">S-1-5-21-*********-*********-*********-120937</Data>
#    <Data Name="SubjectUserSid">S-1-5-21-*********-***********-*********-115455</Data>
#    <Data Name="SubjectUserName">HOSTNAME$</Data>
#    <Data Name="SubjectDomainName">DOMAIN</Data>
#    <Data Name="SubjectLogonId">0x1A96FF7</Data>
#    <Data Name="PrivilegeList">-</Data>
#    <Data Name="SamAccountName">-</Data>
#    <Data Name="DisplayName">-</Data>
#    <Data Name="UserPrincipalName">-</Data>
#    <Data Name="HomeDirectory">-</Data>
#    <Data Name="HomePath">-</Data>
#    <Data Name="ScriptPath">-</Data>
#    <Data Name="ProfilePath">-</Data>
#    <Data Name="UserWorkstations">-</Data>
#    <Data Name="PasswordLastSet">10/2/2020 6:18:59 PM</Data>
#    <Data Name="AccountExpires">-</Data>
#    <Data Name="PrimaryGroupId">-</Data>
#    <Data Name="AllowedToDelegateTo">-</Data>
#    <Data Name="OldUacValue">-</Data>
#    <Data Name="NewUacValue">-</Data>
#    <Data Name="UserAccountControl">-</Data>
#    <Data Name="UserParameters">-</Data>
#    <Data Name="SidHistory">-</Data>
#    <Data Name="LogonHours">-</Data>
#  </EventData>
# </Event>
