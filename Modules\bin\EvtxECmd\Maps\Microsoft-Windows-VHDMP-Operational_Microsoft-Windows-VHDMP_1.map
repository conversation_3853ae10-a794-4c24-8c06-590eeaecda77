Author: <PERSON><PERSON>, <PERSON><PERSON> <PERSON> @hyuunnn
Description: A VHD has been created
EventId: 1
Channel: "Microsoft-Windows-VHDMP/Operational"
Provider: Microsoft-Windows-VHDMP
Maps:
  -
    Property: PayloadData1
    PropertyValue: "The VHD %VhdName% has been created (surfaced) as disk number %VhdNumber%"
    Values:
      -
        Name: VhdName
        Value: "/Event/EventData/Data[@Name=\"VhdFileName\"]"
      -
        Name: VhdNumber
        Value: "/Event/EventData/Data[@Name=\"VhdDiskNumber\"]"

# Documentation:
# https://nasbench.medium.com/finding-forensic-goodness-in-obscure-windows-event-logs-60e978ea45a3
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#     <Provider Name="Microsoft-Windows-VHDMP" Guid="{GUID}" />
#     <EventID>1</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>1205</Task>
#     <Opcode>2</Opcode>
#     <Keywords>0x8000000000000001</Keywords>
#     <TimeCreated SystemTime="2020-12-29T02:31:57.0588526Z" />
#     <EventRecordID>3316</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="4" ThreadID="14296" />
#     <Channel>Microsoft-Windows-VHDMP-Operational</Channel>
#     <Computer>ComputerName</Computer>
#     <Security UserID="{UserID}" />
#   </System>
#   <EventData>
#     <Data Name="VhdFileName">C:\Users\<USER>\Desktop\test.vhd</Data>
#     <Data Name="VhdDiskNumber">3</Data>
#     <Data Name="VirtualDisk">0xffffdf0130cd8280</Data>
#   </EventData>
# </Event>
