Author: <PERSON>
Description: WmiEvent (WmiEventConsumer activity detected)
EventId: 20
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "%Destination%"
    Values:
      -
        Name: Destination
        Value: "/Event/EventData/Data[@Name=\"Destination\"]"
  -
    Property: PayloadData1
    PropertyValue: "Operation: %Operation%"
    Values:
      -
        Name: Operation
        Value: "/Event/EventData/Data[@Name=\"Operation\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "EventType: %EventType%"
    Values:
      -
        Name: EventType
        Value: "/Event/EventData/Data[@Name=\"EventType\"]"
  -
    Property: PayloadData4
    PropertyValue: "Name: %Name%"
    Values:
      -
        Name: Name
        Value: "/Event/EventData/Data[@Name=\"Name\"]"
  -
    Property: PayloadData5
    PropertyValue: "Type: %Type%"
    Values:
      -
        Name: Type
        Value: "/Event/EventData/Data[@Name=\"Type\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%Destination%"
    Values:
      -
        Name: Destination
        Value: "/Event/EventData/Data[@Name=\"Destination\"]"
  -
    Property: UserName
    PropertyValue: "%User%"
    Values:
      -
        Name: User
        Value: "/Event/EventData/Data[@Name=\"User\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>20</EventID>
#    <Version>3</Version>
#    <Level>4</Level>
#    <Task>20</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-07-19 14:54:58.8191060" />
#    <EventRecordID>4056</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="2796" ThreadID="1776" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>MSEDGEWIN10</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName"></Data>
#    <Data Name="EventType">WmiConsumerEvent</Data>
#    <Data Name="UtcTime">2019-07-19 14:54:58.807</Data>
#    <Data Name="Operation">Created</Data>
#    <Data Name="User">MSEDGEWIN10\IEUser</Data>
#    <Data Name="Name"> "AtomicRedTeam-WMIPersistence-Example"</Data>
#    <Data Name="Type">Command Line</Data>
#    <Data Name="Destination"> "C:\\Windows\\System32\\notepad.exe"</Data>
#  </EventData>
# </Event>
