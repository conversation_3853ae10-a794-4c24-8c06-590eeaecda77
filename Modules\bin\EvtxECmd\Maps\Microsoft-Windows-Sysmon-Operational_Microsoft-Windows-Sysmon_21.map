Author: <PERSON>
Description: WmiEvent (WmiEventConsumerToFilter activity detected)
EventId: 21
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Operation: %Operation%"
    Values:
      -
        Name: Operation
        Value: "/Event/EventData/Data[@Name=\"Operation\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "EventType: %EventType%"
    Values:
      -
        Name: EventType
        Value: "/Event/EventData/Data[@Name=\"EventType\"]"
  -
    Property: PayloadData4
    PropertyValue: "Consumer: %Consumer%"
    Values:
      -
        Name: Consumer
        Value: "/Event/EventData/Data[@Name=\"Consumer\"]"
  -
    Property: PayloadData5
    PropertyValue: "Filter: %Filter%"
    Values:
      -
        Name: Filter
        Value: "/Event/EventData/Data[@Name=\"Filter\"]"
  -
    Property: UserName
    PropertyValue: "%User%"
    Values:
      -
        Name: User
        Value: "/Event/EventData/Data[@Name=\"User\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>21</EventID>
#    <Version>3</Version>
#    <Level>4</Level>
#    <Task>21</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-07-19 14:57:02.3784804" />
#    <EventRecordID>4057</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="2796" ThreadID="4356" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>MSEDGEWIN10</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName"></Data>
#    <Data Name="EventType">WmiBindingEvent</Data>
#    <Data Name="UtcTime">2019-07-19 14:57:02.369</Data>
#    <Data Name="Operation">Created</Data>
#    <Data Name="User">MSEDGEWIN10\IEUser</Data>
#    <Data Name="Consumer"> "\\\\.\\ROOT\\subscription:CommandLineEventConsumer.Name=\"AtomicRedTeam-WMIPersistence-Example\""</Data>
#    <Data Name="Filter"> "\\\\.\\ROOT\\subscription:__EventFilter.Name=\"AtomicRedTeam-WMIPersistence-Example\""</Data>
#  </EventData>
# </Event>
