Author: <PERSON>
Description: R<PERSON> authentication request
EventId: 5129
Channel: Varonis
Provider: "VrnsCifsQueue"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%Data%"
    Values:
      -
        Name: Data
        Value: "/Event/EventData/Data"

# Documentation:
# There is no public documentation on these events. Varonis is a data security platform so some file system activity appears to be tracked by it.
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="VrnsCifsQueue" />
#    <EventID Qualifiers="0">5129</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000040000000</Keywords>
#    <TimeCreated SystemTime="2020-08-15 07:10:58.0000000" />
#    <EventRecordID>679123</EventRecordID>
#    <Channel>Varonis</Channel>
#    <Computer>HOSTNAME.domain.com</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>VrnsCifsQueue, 1234, 5678, RPC authentication request from ********** by user "DOMAIN\username" was accepted by the server.
# [Authentication Level = 0]
# </Data>
#    <Binary></Binary>
#  </EventData>
# </Event>
