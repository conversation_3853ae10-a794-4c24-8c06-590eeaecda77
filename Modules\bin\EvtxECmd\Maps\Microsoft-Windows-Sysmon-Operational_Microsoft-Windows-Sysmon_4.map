Author: <PERSON>
Description: Sysmon service state changed
EventId: 4
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:

  -
    Property: PayloadData1
    PropertyValue: "Sysmon Version %Version%"
    Values:
      -
        Name: Version
        Value: "/Event/EventData/Data[@Name=\"Version\"]"
  -
    Property: PayloadData2
    PropertyValue: "Status: %State%"
    Values:
      -
        Name: State
        Value: "/Event/EventData/Data[@Name=\"State\"]"
  -
    Property: PayloadData3
    PropertyValue: "Timestamp: %Timestamp%"
    Values:
      -
        Name: Timestamp
        Value: "/Event/EventData/Data[@Name=\"UtcTime\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>4</EventID>
#    <Version>3</Version>
#    <Level>4</Level>
#    <Task>4</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2020-08-12 13:05:19.7196601" />
#    <EventRecordID>342405</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="3344" ThreadID="4176" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>MSEDGEWIN10</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="UtcTime">2020-08-12 13:05:19.713</Data>
#    <Data Name="State">Started</Data>
#    <Data Name="Version">10.42</Data>
#    <Data Name="SchemaVersion">4.23</Data>
#  </EventData>
# </Event>
