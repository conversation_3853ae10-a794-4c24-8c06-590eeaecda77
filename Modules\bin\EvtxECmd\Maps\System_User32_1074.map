Author: <PERSON>
Description: A user initiated a system restart
EventId: 1074
Channel: System
Provider: User32
Maps:
  -
    Property: UserName
    PropertyValue: "%user%"
    Values:
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"param7\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%ProcessName%"
    Values:
      -
        Name: ProcessName
        Value: "/Event/EventData/Data[@Name=\"param1\"]"
  -
    Property: PayloadData1
    PropertyValue: "Hostname: %Hostname%"
    Values:
      -
        Name: Hostname
        Value: "/Event/EventData/Data[@Name=\"param2\"]"
  -
    Property: PayloadData2
    PropertyValue: "Reason: %Reason%"
    Values:
      -
        Name: Reason
        Value: "/Event/EventData/Data[@Name=\"param3\"]"
  -
    Property: PayloadData3
    PropertyValue: "Type: %Type%"
    Values:
      -
        Name: Type
        Value: "/Event/EventData/Data[@Name=\"param5\"]"
  -
    Property: PayloadData4
    PropertyValue: "Code: %Code%"
    Values:
      -
        Name: Code
        Value: "/Event/EventData/Data[@Name=\"param4\"]"

# Documentation:
# https://superuser.com/questions/767143/find-out-why-pc-just-restarted-for-no-reason
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="User32" Guid="{b0aa8734-56f7-41cc-b2f4-de628e98b946}" EventSourceName="User32" />
#    <EventID Qualifiers="32768">1074</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8080000090000000</Keywords>
#    <TimeCreated SystemTime="2020-10-24 15:22:54.3699307" />
#    <EventRecordID>332</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="676" ThreadID="3576" />
#    <Channel>System</Channel>
#    <Computer>HOSTNAME</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="param1">C:\WINDOWS\system32\winlogon.exe (ANDREW-PERSONAL)</Data>
#    <Data Name="param2">HOSTNAME</Data>
#    <Data Name="param3">Operating System: Upgrade (Planned)</Data>
#    <Data Name="param4">0x80020003</Data>
#    <Data Name="param5">restart</Data>
#    <Data Name="param6"></Data>
#    <Data Name="param7">NT AUTHORITY\SYSTEM</Data>
#  </EventData>
# </Event>
