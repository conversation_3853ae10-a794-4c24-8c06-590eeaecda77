Author: <PERSON> sa<PERSON><EMAIL>
Description: Event log cleared
EventId: 1102
Channel: Security
Provider: Microsoft-Windows-Eventlog
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/UserData/LogFileCleared/SubjectDomainName"
      -
        Name: user
        Value: "/Event/UserData/LogFileCleared/SubjectUserName"
  -
    Property: PayloadData1
    PropertyValue: "SID: (%SubjectUserSid%)"
    Values:
      -
        Name: SubjectUserSid
        Value: "/Event/UserData/LogFileCleared/SubjectUserSid"

# Documentation:
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-1102
#
# Example Event Data:
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-Eventlog" Guid="{fc65ddd8-d6ef-4962-83d5-6e5cfe9ce148}" />
#     <EventID>1102</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>104</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x4020000000000000</Keywords>
#     <TimeCreated SystemTime="2018-05-04 22:14:29.1305755" />
#     <EventRecordID>494</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="1444" ThreadID="256" />
#     <Channel>Security</Channel>
#     <Computer>win10-test</Computer>
#     <Security />
#   </System>
#   <UserData>
#     <LogFileCleared>
#       <SubjectUserSid>S-1-5-21-**********-**********-**********-500</SubjectUserSid>
#       <SubjectUserName>Administrator</SubjectUserName>
#       <SubjectDomainName>WIN10-TEST</SubjectDomainName>
#       <SubjectLogonId>0x21FCB6</SubjectLogonId>
#     </LogFileCleared>
#   </UserData>
# </Event>
