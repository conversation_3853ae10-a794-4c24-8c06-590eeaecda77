Author: <PERSON>
Description: Provider is Started. 
EventId: 600
Channel: Windows PowerShell
Maps: 
  - 
    Property: PayloadData1
    PropertyValue: "%HostApplication%"
    Values: 
      - 
        Name: HostApplication
        Value: "/Event/EventData/Data"
        Refine: "HostApplication=(.+)"
  - 
    Property: PayloadData2
    PropertyValue: "%HostName%"
    Values: 
      - 
        Name: HostName
        Value: "/Event/EventData/Data"
        Refine: "HostName=(.+)"
  - 
    Property: PayloadData3
    PropertyValue: "%HostVersion%"
    Values: 
      - 
        Name: HostVersion
        Value: "/Event/EventData/Data"
        Refine: "HostVersion=(.+)"
# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6
# Example XML for this event:
#<Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#  <System>
#    <Provider Name="PowerShell" /> 
#    <EventID Qualifiers="0">600</EventID> 
#    <Level>4</Level> 
#    <Task>6</Task> 
#    <Keywords>0x80000000000000</Keywords> 
#    <TimeCreated SystemTime="2001-01-01T01:01:01.012345678Z" /> 
#    <EventRecordID>18</EventRecordID> 
#    <Channel>Windows PowerShell</Channel> 
#    <Computer>name.domain.tld</Computer> 
#    <Security /> 
#  </System>
#  <EventData>
#    <Data>Registry, Started, 	ProviderName=Registry
#	NewProviderState=Started
#
#	SequenceNumber=1
#
#	HostName=ConsoleHost
#	HostVersion=5.1.18362.145
#	HostId=b3dfcb89-d2f8-4b8b-a784-a6a9bcf61bd8
#	HostApplication=powershell  -command Set-ItemProperty -Path HKCU:\Software\Microsoft\Office\16.0\Outlook\AutoDiscover -Name 'ExcludeExplicitO365Endpoint' -Value 1 -Type DWORD -Force 
#	EngineVersion=
#	RunspaceId=
#	PipelineId=
#	CommandName=
#	CommandType=
#	ScriptName=
#	CommandPath=
#	CommandLine=</Data>
#	</EventData>
#</Event>