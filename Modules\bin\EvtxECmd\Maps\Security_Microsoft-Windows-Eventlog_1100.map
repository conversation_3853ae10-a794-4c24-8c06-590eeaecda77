Author: <PERSON>
Description: The event logging service has shut down
EventId: 1100
Channel: Security
Provider: "Microsoft-Windows-Eventlog"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%ServiceShutdown%"
    Values:
      -
        Name: ServiceShutdown
        Value: "/Event/UserData[@Name=\"ServiceShutdown\"]"

# Documentation:
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-1102
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventID=1100
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4609
# Please note, Event Log 4609 (Computer is shutting down) doesn't get logged anymore due to this service being shut down first.
# Therefore, 4609 never gets a chance to get logged. This log is effectively your time of computer shutdown, as a result.
# This map likely won't log any data in PayloadData1, but at least this map will provide the Map Description column with something.
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Eventlog" Guid="{fc64rgd8-d6ef-4962-83d5-6e5wergce148}" />
#    <EventID>1100</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>103</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x4020000340000000</Keywords>
#    <TimeCreated SystemTime="2020-10-07 02:38:05.9085508" />
#    <EventRecordID>266791475</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="679" ThreadID="1234" />
#    <Channel>Security</Channel>
#    <Computer>HOSTNAME</Computer>
#    <Security />
#  </System>
#  <UserData>
#    <ServiceShutdown></ServiceShutdown>
#  </UserData>
# </Event>
