Author: <PERSON>
Description: FileCreateStreamHash
EventId: 15
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: PayloadData1
    PropertyValue: "ProcessID: %ProcessID%, ProcessGUID: %ProcessGUID%"
    Values:
      -
        Name: ProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ProcessGuid\"]"
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "Image: %Image%"
    Values:
      -
        Name: Image
        Value: "/Event/EventData/Data[@Name=\"Image\"]"
  -
    Property: PayloadData4
    PropertyValue: "TargetFilename: %TargetFilename%"
    Values:
      -
        Name: TargetFilename
        Value: "/Event/EventData/Data[@Name=\"TargetFilename\"]"
  -
    Property: PayloadData5
    PropertyValue: "Hash: %Hash%"
    Values:
      -
        Name: Hash
        Value: "/Event/EventData/Data[@Name=\"Hash\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>15</EventID>
#    <Version>2</Version>
#    <Level>4</Level>
#    <Task>15</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-05-09 02:52:23.5002632" />
#    <EventRecordID>11240</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="1988" ThreadID="228" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>IEWIN7</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName"></Data>
#    <Data Name="UtcTime">2019-05-09 02:52:23.500</Data>
#    <Data Name="ProcessGuid">365abb72-95e7-5cd3-0000-001046950f00</Data>
#    <Data Name="ProcessId">2812</Data>
#    <Data Name="Image">C:\Windows\system32\cmd.exe</Data>
#    <Data Name="TargetFilename">C:\Users\<USER>\AppData</Data>
#    <Data Name="CreationUtcTime">2018-01-03 01:21:25.726</Data>
#    <Data Name="Hash">Unknown</Data>
#  </EventData>
# </Event>
