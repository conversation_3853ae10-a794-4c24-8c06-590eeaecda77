Author: <PERSON><PERSON> <PERSON> @hyuunnn
Description: OS was started
EventId: 12
Channel: System
Provider: Microsoft-Windows-Kernel-General
Maps:
  -
    Property: PayloadData1
    PropertyValue: "StartTime: %StartTime%"
    Values:
      -
        Name: StartTime
        Value: "/Event/EventData/Data[@Name=\"StartTime\"]"
  -
    Property: PayloadData2
    PropertyValue: "BootMode: %BootMode%"
    Values:
      -
        Name: BootMode
        Value: "/Event/EventData/Data[@Name=\"BootMode\"]"
Lookups:
  -
    Name: BootMode
    Default: Unknown code
    Values:
      0: Normal boot
      1: Safe Mode boot

# Documentation:
# https://www.eventid.net/display-eventid-12-source-Microsoft-Windows-Kernel-General-eventno-11542-phase-1.htm
# https://social.technet.microsoft.com/Forums/azure/en-US/c4281d71-0152-473a-8abf-80ab1694f24d/event-id12?forum=win10itprogeneral
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#     <Provider Name="Microsoft-Windows-Kernel-General" Guid="{GUID}" />
#     <EventID>12</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>1</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000000000000080</Keywords>
#     <TimeCreated SystemTime="2020-10-06T16:30:37.2169174Z" />
#     <EventRecordID>3</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="4" ThreadID="8" />
#     <Channel>System</Channel>
#     <Computer>ComputerName</Computer>
#     <Security UserID="S-1-5-18" />
#   </System>
#   <EventData>
#     <Data Name="MajorVersion">10</Data>
#     <Data Name="MinorVersion">0</Data>
#     <Data Name="BuildVersion">19041</Data>
#     <Data Name="QfeVersion">508</Data>
#     <Data Name="ServiceVersion">0</Data>
#     <Data Name="BootMode">0</Data>
#     <Data Name="StartTime">2020-10-06T16:30:36.5000000Z</Data>
#   </EventData>
# </Event>
