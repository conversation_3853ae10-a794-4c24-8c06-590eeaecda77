Author: <PERSON><PERSON> <PERSON> @hyuunnn
Description: USB Connection
EventId: 1001
Channel: "Microsoft-Windows-Storsvc/Diagnostic"
Provider: "Microsoft-Windows-Storsvc"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "VendorId: %VendorId%"
    Values:
      -
        Name: VendorId
        Value: "/Event/EventData/Data[@Name=\"VendorId\"]"
  -
    Property: PayloadData2
    PropertyValue: "ProductId: %ProductId%"
    Values:
      -
        Name: ProductId
        Value: "/Event/EventData/Data[@Name=\"ProductId\"]"
  -
    Property: PayloadData3
    PropertyValue: "SerialNumber: %SerialNumber%"
    Values:
      -
        Name: SerialNumber
        Value: "/Event/EventData/Data[@Name=\"SerialNumber\"]"
  -
    Property: PayloadData4
    PropertyValue: "Size: %Size% Bytes"
    Values:
      -
        Name: Size
        Value: "/Event/EventData/Data[@Name=\"Size\"]"
  -
    Property: PayloadData5
    PropertyValue: "FileSystem: %FileSystem%"
    Values:
      -
        Name: FileSystem
        Value: "/Event/EventData/Data[@Name=\"FileSystem\"]"
  -
    Property: PayloadData6
    PropertyValue: "BusType: %BusType%"
    Values:
      -
        Name: BusType
        Value: "/Event/EventData/Data[@Name=\"BusType\"]"
Lookups:
  -
    Name: BusType
    Default: Unknown code
    Values:
      0: The bus type is unknown.
      1: SCSI
      2: ATAPI
      3: ATA
      4: IEEE 1394
      5: SSA
      6: Fibre Channel
      7: USB
      8: RAID
      9: iSCSI
      10: Serial Attached SCSI (SAS)
      11: Serial ATA (SATA)
      12: Secure Digital (SD)
      13: Multimedia Card (MMC)
      14: This value is reserved for system use.
      15: File-Backed Virtual
      16: Storage Spaces
      17: NVMe
      18: This value is reserved for system use.

# Documentation:
# https://forensixchange.com/posts/19_08_03_usb_storage_forensics_1/
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#     <Provider Name="Microsoft-Windows-Storsvc" Guid="{GUID}" />
#     <EventID>1001</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>0</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000000000000000</Keywords>
#     <TimeCreated SystemTime="2020-12-08T01:03:35.5243959Z" />
#     <EventRecordID>220</EventRecordID>
#     <Correlation ActivityID="{ActivityID}" />
#     <Execution ProcessID="7796" ThreadID="12988" />
#     <Channel>Microsoft-Windows-Storsvc/Diagnostic</Channel>
#     <Computer>ComputerName</Computer>
#     <Security UserID="{UserID}" />
#   </System>
#   <EventData>
#     <Data Name="Version">2</Data>
#     <Data Name="DiskNumber">1</Data>
#     <Data Name="VendorId">WD</Data>
#     <Data Name="ProductId">My Passport 25E2</Data>
#     <Data Name="ProductRevision">4005</Data>
#     <Data Name="SerialNumber">WX41D69CD7D1</Data>
#     <Data Name="ParentId">USB\VID_1058&PID_25E2\575834314436394344374431</Data>
#     <Data Name="FileSystem">NTFS</Data>
#     <Data Name="BusType">7</Data>
#     <Data Name="PartitionStyle">1</Data>
#     <Data Name="VolumeCount">1</Data>
#     <Data Name="ContainsRawVolumes">false</Data>
#     <Data Name="Size">4000752599040</Data>
#   </EventData>
# </Event>
