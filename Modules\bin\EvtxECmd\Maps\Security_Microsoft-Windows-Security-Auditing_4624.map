Author: <PERSON> sa<PERSON><EMAIL>
Description: Successful logon
EventId: 4624
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  -
    Property: RemoteHost
    PropertyValue: "%workstation% (%ipAddress%)"
    Values:
      -
        Name: ipAddress
        Value: "/Event/EventData/Data[@Name=\"IpAddress\"]"
      -
        Name: workstation
        Value: "/Event/EventData/Data[@Name=\"WorkstationName\"]"
  -
    Property: PayloadData1
    PropertyValue: "Target: %TargetDomainName%\\%TargetUserName%"
    Values:
      -
        Name: TargetDomainName
        Value: "/Event/EventData/Data[@Name=\"TargetDomainName\"]"
      -
        Name: TargetUserName
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
  -
    Property: PayloadData2
    PropertyValue: "LogonType %LogonType%"
    Values:
      -
        Name: LogonType
        Value: "/Event/EventData/Data[@Name=\"LogonType\"]"
  -
    Property: PayloadData3
    PropertyValue: "LogonId: %TargetLogonId%"
    Values:
      -
        Name: TargetLogonId
        Value: "/Event/EventData/Data[@Name=\"TargetLogonId\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%ProcessName%"
    Values:
      -
        Name: ProcessName
        Value: "/Event/EventData/Data[@Name=\"ProcessName\"]"

# Documentation:
# https://ponderthebits.com/2018/02/windows-rdp-related-event-logs-identification-tracking-and-investigation/
# https://www.13cubed.com/downloads/rdp_flowchart.pdf
# https://dfironthemountain.wordpress.com/2019/02/15/rdp-event-log-dfir/
# http://woshub.com/rdp-connection-logs-forensics-windows/
# https://countuponsecurity.com/2015/11/25/digital-forensics-supertimeline-event-logs-part-ii/
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4624
# https://frsecure.com/blog/rdp-connection-event-logs/
#
# Example Event Data:
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-Security-Auditing" Guid="*************-4994-a5ba-3e3b0328c30d" />
#     <EventID>4624</EventID>
#     <Version>2</Version>
#     <Level>0</Level>
#     <Task>12544</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8020000000000000</Keywords>
#     <TimeCreated SystemTime="2018-09-06 20:26:07.9341912" />
#     <EventRecordID>57241</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="776" ThreadID="780" />
#     <Channel>Security</Channel>
#     <Computer>base-rd-01.shieldbase.lan</Computer>
#     <Security />
#   </System>
#   <EventData>
#     <Data Name="SubjectUserSid">S-1-0-0</Data>
#     <Data Name="SubjectUserName">-</Data>
#     <Data Name="SubjectDomainName">-</Data>
#     <Data Name="SubjectLogonId">0x0</Data>
#     <Data Name="TargetUserSid">S-1-5-18</Data>
#     <Data Name="TargetUserName">SYSTEM</Data>
#     <Data Name="TargetDomainName">NT AUTHORITY</Data>
#     <Data Name="TargetLogonId">0x3E7</Data>
#     <Data Name="LogonType">0</Data>
#     <Data Name="LogonProcessName">-</Data>
#     <Data Name="AuthenticationPackageName">-</Data>
#     <Data Name="WorkstationName">-</Data>
#     <Data Name="LogonGuid">00000000-0000-0000-0000-000000000000</Data>
#     <Data Name="TransmittedServices">-</Data>
#     <Data Name="LmPackageName">-</Data>
#     <Data Name="KeyLength">0</Data>
#     <Data Name="ProcessId">0x4</Data>
#     <Data Name="ProcessName"></Data>
#     <Data Name="IpAddress">-</Data>
#     <Data Name="IpPort">-</Data>
#     <Data Name="ImpersonationLevel">-</Data>
#     <Data Name="RestrictedAdminMode">-</Data>
#     <Data Name="TargetOutboundUserName">-</Data>
#     <Data Name="TargetOutboundDomainName">-</Data>
#     <Data Name="VirtualAccount">%%1843</Data>
#     <Data Name="TargetLinkedLogonId">0x0</Data>
#     <Data Name="ElevatedToken">%%1842</Data>
#   </EventData>
# </Event>
