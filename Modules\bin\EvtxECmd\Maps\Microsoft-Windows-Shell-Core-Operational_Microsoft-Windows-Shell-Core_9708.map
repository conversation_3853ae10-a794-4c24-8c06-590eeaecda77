Author: <PERSON>
Description: Finished execution of command
EventId: 9708
Channel: Microsoft-Windows-Shell-Core/Operational
Provider: Microsoft-Windows-Shell-Core
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "%CommandLine%"
    Values:
      -
        Name: CommandLine
        Value: "/Event/EventData/Data[@Name=\"Command\"]"
  -
    Property: PayloadData2
    PropertyValue: "PID: %ProcessId%"
    Values:
      -
        Name: ProcessId
        Value: "/Event/EventData/Data[@Name=\"PID\"]"

# Documentation:
# https://www.geoffchappell.com/notes/windows/shell/events/core.htm
# https://nasbench.medium.com/finding-forensic-goodness-in-obscure-windows-event-logs-60e978ea45a3
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Shell-Core" Guid="30336ed4-e327-447c-9de0-51b652c86108" />
#    <EventID>9708</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>9707</Task>
#    <Opcode>2</Opcode>
#    <Keywords>0x2000000004010000</Keywords>
#    <TimeCreated SystemTime="" />
#    <EventRecordID></EventRecordID>
#    <Correlation />
#    <Execution ProcessID="" ThreadID="" />
#    <Channel>Microsoft-Windows-Shell-Core/Operational</Channel>
#    <Computer></Computer>
#    <Security UserID="" />
#  </System>
#  <EventData>
#    <Data Name="PID">2052</Data>
#    <Data Name="Command">BrCcBoot.exe /autorun</Data>
#  </EventData>
# </Event>
