Author: <PERSON> <EMAIL>
Description: A scheduled task was disabled
EventId: 4701
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  -
    Property: PayloadData1
    PropertyValue: "<name>: %<name>%"
    Values:
      -
        Name: <name>
        Value: "/Event/EventData/Data[@Name=\"<name>\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4701
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4701
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Security-Auditing" Guid="*************-4994-a5ba-3e3y483c30d" />
#    <EventID>4701</EventID>
#    <Version>0</Version>
#    <Level>0</Level>
#    <Task>12345</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8020000420000000</Keywords>
#    <TimeCreated SystemTime="2020-10-12 20:49:24.5768395" />
#    <EventRecordID>476798331</EventRecordID>
#    <Correlation ActivityID="1eba132e-a0f9-0044-3293-ba1efab0d601" />
#    <Execution ProcessID="679" ThreadID="100" />
#    <Channel>Security</Channel>
#    <Computer>HOSTNAME.DOMAIN.net</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="SubjectUserSid">S-1-5-18</Data>
#    <Data Name="SubjectUserName">USERNAME</Data>
#    <Data Name="SubjectDomainName">DOMAIN</Data>
#    <Data Name="SubjectLogonId">0x3E5</Data>
#    <Data Name="TaskName">\Microsoft\Windows\UpdateOrchestrator\Resume On Boot</Data>
#    <Data Name="TaskContent">&amp;lt;?xml version="1.0" encoding="UTF-16"?&amp;gt;, &amp;lt;Task version="1.2"&amp;gt;,     &amp;lt;Exec&amp;gt;,       &amp;lt;Command&amp;gt;%systemroot%\system32\abcclient.exe&amp;lt;/Command&amp;gt;,       &amp;lt;Arguments&amp;gt;ResumeUpdate&amp;lt;/Arguments&amp;gt;,     &amp;lt;/Exec&amp;gt;,   &amp;lt;/Actions&amp;gt;, &amp;lt;/Task&amp;gt;</Data>
#  </EventData>
# </Event>
