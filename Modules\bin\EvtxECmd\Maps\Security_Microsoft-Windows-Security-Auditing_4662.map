Author: <PERSON>
Description: Operation performed on an object
EventId: 4662
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  -
    Property: PayloadData1
    PropertyValue: "ObjectServer: %ObjectServer%"
    Values:
      -
        Name: ObjectServer
        Value: "/Event/EventData/Data[@Name=\"ObjectServer\"]"
  -
    Property: PayloadData2
    PropertyValue: "ObjectType: %ObjectType%"
    Values:
      -
        Name: ObjectType
        Value: "/Event/EventData/Data[@Name=\"ObjectType\"]"
  -
    Property: PayloadData3
    PropertyValue: "ObjectName: %ObjectName%"
    Values:
      -
        Name: ObjectName
        Value: "/Event/EventData/Data[@Name=\"ObjectName\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4662
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4662
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-A5BA-3E3B0328C30D}" />
# <EventID>4662</EventID>
# <Version>0</Version>
# <Level>0</Level>
# <Task>14080</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8020000000000000</Keywords>
# <TimeCreated SystemTime="2015-08-28T01:58:36.894922400Z" />
# <EventRecordID>407230</EventRecordID>
# <Correlation />
# <Execution ProcessID="520" ThreadID="600" />
# <Channel>Security</Channel>
# <Computer>DC01.contoso.local</Computer>
# <Security />
# </System>
# <EventData>
# <Data Name="SubjectUserSid">S-1-5-21-**********-**********-823803824-1104</Data>
# <Data Name="SubjectUserName">dadmin</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x35867</Data>
# <Data Name="ObjectServer">DS</Data>
# <Data Name="ObjectType">%{bf967a86-0de6-11d0-a285-00aa003049e2}</Data>
# <Data Name="ObjectName">%{38b3d2e6-9948-4dc1-ae90-1605d5eab9a2}</Data>
# <Data Name="OperationType">Object Access</Data>
# <Data Name="HandleId">0x0</Data>
# <Data Name="AccessList">%%1537</Data>
# <Data Name="AccessMask">0x10000</Data>
# <Data Name="Properties">%%1537 {bf967a86-0de6-11d0-a285-00aa003049e2}</Data>
# <Data Name="AdditionalInfo">-</Data>
# <Data Name="AdditionalInfo2" />
# </EventData>
# </Event>
