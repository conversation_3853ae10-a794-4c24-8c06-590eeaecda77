Author: <PERSON><PERSON> <PERSON> @hyuunnn
Description: Synchronizing system time from time service
EventId: 35
Channel: "System"
Provider: "Microsoft-Windows-Time-Service"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "ServerAddress: %ServerAddress%"
    Values:
      -
        Name: ServerAddress
        Value: "/Event/EventData/Data[@Name=\"TimeSource\"]"
        Refine: "^.*(?=,)"
  -
    Property: PayloadData2
    PropertyValue: "ServerIP: %ServerIP%"
    Values:
      -
        Name: ServerIP
        Value: "/Event/EventData/Data[@Name=\"TimeSource\"]"
        Refine: "(?<=-&gt;).*(?=))"

# Documentation:
# N/A
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#     <Provider Name="Microsoft-Windows-Time-Service" Guid="{GUID}" />
#     <EventID>35</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>0</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000000000000000</Keywords>
#     <TimeCreated SystemTime="2020-11-08T17:55:55.8107139Z" />
#     <EventRecordID>5856</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="12840" ThreadID="4544" />
#     <Channel>System</Channel>
#     <Computer>ComputerName</Computer>
#     <Security UserID="{UserID}" />
#   </System>
#   <EventData Name="TMP_EVENT_TIME_SOURCE_CHOSEN">
#     <Data Name="TimeSource">time.windows.com,0x9 (ntp.m|0x9|0.0.0.0:123->**************:123)</Data>
#     <Data Name="TimeSourceRefId">**********</Data>
#     <Data Name="CurrentStratumNumber">4</Data>
#   </EventData>
# </Event>
