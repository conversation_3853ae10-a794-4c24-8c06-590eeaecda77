Description: 'NTFS Log Tracker: process $LogFile files'
Category: FileSystem
Author: <PERSON><PERSON> @hyuunnn
Version: 1.0
Id: 03939a50-0325-49f0-8cad-1f35c083f44b
BinaryUrl: https://sites.google.com/site/forensicnote/ntfs-log-tracker/NTFS Log Tracker v1.6 CMD.zip
ExportFormat: sqlite3
FileMask: $LogFile
Processors:
    -
        Executable: NTFS Log Tracker v1.6 CMD\NTFS_Log_Tracker_CMD_V1.6.exe
        CommandLine: -l %sourceFile% -o %destinationDirectory%
        ExportFormat: sqlite3
    -
        Executable: NTFS Log Tracker v1.6 CMD\NTFS_Log_Tracker_CMD_V1.6.exe
        CommandLine: -l %sourceFile% -o %destinationDirectory% -c
        ExportFormat: csv

# Documentation
# https://sites.google.com/site/forensicnote/ntfs-log-tracker
