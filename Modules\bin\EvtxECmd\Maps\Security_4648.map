Author: <PERSON> sa<PERSON><EMAIL>
Description: A logon was attempted using explicit credentials
EventId: 4648
Channel: Security
Maps: 
  - 
    Property: Username
    PropertyValue: "%domain%\\%user%"
    Values: 
      - 
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      - 
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  - 
    Property: PayloadData1
    PropertyValue: "Target: %TargetDomainName%\\%TargetUserName%"
    Values: 
      - 
        Name: TargetDomainName
        Value: "/Event/EventData/Data[@Name=\"TargetDomainName\"]"
      - 
        Name: TargetUserName
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
  - 
    Property: PayloadData2
    PropertyValue: "TargetServerName: %TargetServerName%"
    Values: 
      - 
        Name: TargetServerName
        Value: "/Event/EventData/Data[@Name=\"TargetServerName\"]"
  - 
    Property: RemoteHost
    PropertyValue: "%ipAddress%:%port%"
    Values: 
      - 
        Name: ipAddress
        Value: "/Event/EventData/Data[@Name=\"IpAddress\"]"
      - 
        Name: port
        Value: "/Event/EventData/Data[@Name=\"IpPort\"]"
  - 
    Property: ExecutableInfo
    PropertyValue: "%ProcessName%"
    Values: 
      - 
        Name: ProcessName
        Value: "/Event/EventData/Data[@Name=\"ProcessName\"]"
  -
    Property: PayloadData3
    PropertyValue: "PID: %ProcessId%"
    Values: 
      - 
        Name: ProcessId
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
#<Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Security-Auditing" Guid="*************-4994-a5ba-3e3b0328c30d" />
#    <EventID>4648</EventID>
#    <Version>0</Version>
#    <Level>0</Level>
#    <Task>12544</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8020000000000000</Keywords>
#    <TimeCreated SystemTime="2018-07-11 05:42:11.2819203" />
#    <EventRecordID>24411</EventRecordID>
#    <Correlation ActivityID="3fd16c43-fc41-0001-ad6c-d13f41fcd301" />
#    <Execution ProcessID="744" ThreadID="2756" />
#    <Channel>Security</Channel>
#    <Computer>base-rd-01.shieldbase.lan</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="SubjectUserSid">S-1-5-20</Data>
#    <Data Name="SubjectUserName">BASE-RD-01$</Data>
#    <Data Name="SubjectDomainName">shieldbase</Data>
#    <Data Name="SubjectLogonId">0x3E4</Data>
#    <Data Name="LogonGuid">00000000-0000-0000-0000-000000000000</Data>
#    <Data Name="TargetUserName">tdungan</Data>
#    <Data Name="TargetDomainName">shieldbase</Data>
#    <Data Name="TargetLogonGuid">00000000-0000-0000-0000-000000000000</Data>
#    <Data Name="TargetServerName">localhost</Data>
#    <Data Name="TargetInfo">localhost</Data>
#    <Data Name="ProcessId">0x1D0</Data>
#    <Data Name="ProcessName">C:\Windows\System32\svchost.exe</Data>
#    <Data Name="IpAddress">-</Data>
#    <Data Name="IpPort">-</Data>
#  </EventData>
#</Event>
