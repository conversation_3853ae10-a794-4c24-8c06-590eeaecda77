Description: MFTECmd - Parse $J and $MFT together to produce CSV output with ParentPath populated in $J CSV
Category: FileSystem
Author: <PERSON>
Version: 1.0
Id: ac0660c3-4eb2-4dee-ad90-5ef782b94750
BinaryUrl: https://github.com/AndrewRathbun/DFIRPowerShellScripts/blob/main/MFTECmd%24J%24MFTParser.ps1
ExportFormat: csv
Processors:
    -
        Executable: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe
        CommandLine: "& '%kapeDirectory%\\Modules\\bin\\MFTECmd$J$MFTParser.ps1' -TargetsFolder %sourceDirectory% -OutputFolder %destinationDirectory%"
        ExportFormat: csv

# Documentation
# https://github.com/AndrewRathbun/DFIRPowerShellScripts/blob/main/MFTECmd%24J%24MFTParser.ps1
# Use this when there's a $J and $MFT present. This script will search for the $J and $MFT and parse them with the MFTECmd executable that resides in .\KAPE\Modules\bin
# The main reason for this script's existence is because a KAPE Module cannot have 2 file masks (one for $J and another for $MFT) so this script serves as the workaround for that scenario
# To use this properly, point to a parent folder where somewhere in a child folder there exists a $J and $MFT, ideally from the same system!
