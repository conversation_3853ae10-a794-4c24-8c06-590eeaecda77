Author: <PERSON>
Description: A user right was adjusted
EventId: 4703
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user% (%sid%)"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"SubjectUserSid\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%ProcessName% (PID: %ProcessId%)"
    Values:
      -
        Name: ProcessName
        Value: "/Event/EventData/Data[@Name=\"ProcessName\"]"
      -
        Name: ProcessId
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData1
    PropertyValue: "Target: %domain%\\%user% (%sid%)"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"TargetDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"TargetUserSid\"]"
  -
    Property: PayloadData2
    PropertyValue: "EnabledPrivilegeList: %EnabledPrivilegeList%"
    Values:
      -
        Name: EnabledPrivilegeList
        Value: "/Event/EventData/Data[@Name=\"EnabledPrivilegeList\"]"
  -
    Property: PayloadData3
    PropertyValue: "DisabledPrivilegeList: %DisabledPrivilegeList%"
    Values:
      -
        Name: DisabledPrivilegeList
        Value: "/Event/EventData/Data[@Name=\"DisabledPrivilegeList\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4703
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4703
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-A5BA-3E3B0328C30D}" />
# <EventID>4703</EventID>
# <Version>0</Version>
# <Level>0</Level>
# <Task>13570</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8020000000000000</Keywords>
# <TimeCreated SystemTime="2015-11-12T20:49:46.365958700Z" />
# <EventRecordID>5245</EventRecordID>
# <Correlation />
# <Execution ProcessID="4" ThreadID="3632" />
# <Channel>Security</Channel>
# <Computer>WIN-GG82ULGC9GO.contoso.local</Computer>
# <Security />
# </System>
# <EventData>
# <Data Name="SubjectUserSid">S-1-5-18</Data>
# <Data Name="SubjectUserName">WIN-GG82ULGC9GO$</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x3e7</Data>
# <Data Name="TargetUserSid">S-1-5-18</Data>
# <Data Name="TargetUserName">WIN-GG82ULGC9GO$</Data>
# <Data Name="TargetDomainName">CONTOSO</Data>
# <Data Name="TargetLogonId">0x3e7</Data>
# <Data Name="ProcessName">C:\\Windows\\System32\\svchost.exe</Data>
# <Data Name="ProcessId">0x270</Data>
# <Data Name="EnabledPrivilegeList">SeAssignPrimaryTokenPrivilege SeIncreaseQuotaPrivilege SeSecurityPrivilege SeTakeOwnershipPrivilege SeLoadDriverPrivilege SeSystemtimePrivilege SeBackupPrivilege SeRestorePrivilege SeShutdownPrivilege SeSystemEnvironmentPrivilege SeUndockPrivilege SeManageVolumePrivilege</Data>
# <Data Name="DisabledPrivilegeList">-</Data>
# </EventData>
# </Event>
