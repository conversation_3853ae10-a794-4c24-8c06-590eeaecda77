Author: <PERSON>
Description: System audit policy was changed
EventId: 4719
Channel: Security
Maps: 
  - 
    Property: Username
    PropertyValue: "%domain%\\%user%"
    Values: 
      - 
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      - 
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  - 
    Property: PayloadData1
    PropertyValue: "CategoryId: %CategoryId% SubcategoryId: %SubcategoryId%"
    Values: 
      - 
        Name: CategoryId
        Value: "/Event/EventData/Data[@Name=\"CategoryId\"]"
      - 
        Name: SubcategoryId
        Value: "/Event/EventData/Data[@Name=\"SubcategoryId\"]"
  - 
    Property: PayloadData2
    PropertyValue: "SubcategoryGuid: %SubcategoryGuid%"
    Values: 
      - 
        Name: SubcategoryGuid
        Value: "/Event/EventData/Data[@Name=\"SubcategoryGuid\"]"
  - 
    Property: PayloadData3
    PropertyValue: "AuditPolicyChanges: %AuditPolic<PERSON>Changes%"
    Values: 
      - 
        Name: AuditPolicyChanges
        Value: "/Event/EventData/Data[@Name=\"AuditPolicyChanges\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
#  <EventData>
#    <Data Name="SubjectUserSid">S-1-5-18</Data>
#    <Data Name="SubjectUserName">DC1$</Data>
#    <Data Name="SubjectDomainName">insecurebank</Data>
#    <Data Name="SubjectLogonId">0x3E7</Data>
#    <Data Name="CategoryId">%%8279</Data>
#    <Data Name="SubcategoryId">%%14080</Data>
#    <Data Name="SubcategoryGuid">0cce923b-69ae-11d9-bed3-************</Data>
#    <Data Name="AuditPolicyChanges">%%8451</Data>
#  </EventData>