Author: <PERSON>
Description: <PERSON><PERSON> has finished.
EventId: 1001
Channel: Microsoft-Windows-Windows Defender/Operational
Maps: 
  - 
    Property: UserName
    PropertyValue: "%Domain%\\%User%"
    Values: 
      - 
        Name: Domain
        Value: "/Event/EventData/Data[@Name=\"Domain\"]"
      - 
        Name: User
        Value: "/Event/EventData/Data[@Name=\"User\"]"
  - 
    Property: PayloadData1
    PropertyValue: "Scan ID: %ScanID%"
    Values: 
      - 
        Name: ScanID
        Value: "/Event/EventData/Data[@Name=\"Scan ID\"]"
  - 
    Property: PayloadData2
    PropertyValue: "Scan type\\index: %ScanType%\\%ScanTypeIndex%"
    Values: 
      - 
        Name: ScanType
        Value: "/Event/EventData/Data[@Name=\"Scan Type\"]"
      - 
        Name: ScanTypeIndex
        Value: "/Event/EventData/Data[@Name=\"Scan Type Index\"]"        
  - 
    Property: PayloadData3
    PropertyValue: "Scan parameters\\index: %ScanParameters%\\%ScanParametersIndex%"
    Values: 
      - 
        Name: ScanParameters
        Value: "/Event/EventData/Data[@Name=\"Scan Parameters\"]"
      - 
        Name: ScanParametersIndex
        Value: "/Event/EventData/Data[@Name=\"Scan Parameters Index\"]"
  - 
    Property: PayloadData4
    PropertyValue: "Scan time=%Hours%:%Minutes%:%Seconds%"
    Values: 
      - 
        Name: Hours
        Value: "/Event/EventData/Data[@Name=\"Scan Time Hours\"]"
      - 
        Name: Minutes
        Value: "/Event/EventData/Data[@Name=\"Scan Time Minutes\"]"            
      - 
        Name: Seconds
        Value: "/Event/EventData/Data[@Name=\"Scan Time Seconds\"]"  

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6
# Example XML for this event:
#<Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Windows Defender" Guid="{11cd958a-c507-4ef3-b3f2-5fd9dfbd2c78}" /> 
#    <EventID>1001</EventID> 
#    <Version>0</Version> 
#    <Level>4</Level> 
#    <Task>0</Task> 
#    <Opcode>0</Opcode> 
#    <Keywords>0x8000000000000000</Keywords> 
#    <TimeCreated SystemTime="2019-10-23T04:46:09.394800900Z" /> 
#    <EventRecordID>60657</EventRecordID> 
#    <Correlation ActivityID="{7da69ef7-cdc1-4b48-a3c4-3d719cf4756b}" /> 
#    <Execution ProcessID="5268" ThreadID="13008" /> 
#    <Channel>Microsoft-Windows-Windows Defender/Operational</Channel> 
#    <Computer>Thunder.cloud.corp.com</Computer> 
#    <Security UserID="S-1-5-18" /> 
#  </System>
#  <EventData>
#    <Data Name="Product Name">%%827</Data> 
#    <Data Name="Product Version">4.18.1909.6</Data> 
#    <Data Name="Scan ID">{ADA555A6-3D3E-4946-9DAA-8B4EFCF18A1F}</Data> 
#    <Data Name="Scan Type Index">1</Data> 
#    <Data Name="Scan Type">%%802</Data> 
#    <Data Name="Scan Parameters Index">1</Data> 
#    <Data Name="Scan Parameters">%%806</Data> 
#    <Data Name="Domain">NT AUTHORITY</Data> 
#    <Data Name="User">SYSTEM</Data> 
#    <Data Name="SID">S-1-5-18</Data> 
#    <Data Name="Scan Time Hours">0</Data> 
#    <Data Name="Scan Time Minutes">10</Data> 
#    <Data Name="Scan Time Seconds">38</Data> 
#  </EventData>
#</Event>