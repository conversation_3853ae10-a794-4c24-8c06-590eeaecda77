Author: <PERSON>
Description: RegistryEvent (Key and Value Rename)
EventId: 14
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: PayloadData1
    PropertyValue: "ProcessID: %ProcessID%, ProcessGUID: %ProcessGUID%"
    Values:
      -
        Name: ProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ProcessGuid\"]"
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData3
    PropertyValue: "Image: %Image%"
    Values:
      -
        Name: Image
        Value: "/Event/EventData/Data[@Name=\"Image\"]"
  -
    Property: PayloadData4
    PropertyValue: "EventType: %EventType%"
    Values:
      -
        Name: EventType
        Value: "/Event/EventData/Data[@Name=\"EventType\"]"
  -
    Property: PayloadData5
    PropertyValue: "TargetObject: %TargetObject%"
    Values:
      -
        Name: TargetObject
        Value: "/Event/EventData/Data[@Name=\"TargetObject\"]"
  -
    Property: PayloadData6
    PropertyValue: "NewName: %NewName%"
    Values:
      -
        Name: NewName
        Value: "/Event/EventData/Data[@Name=\"NewName\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#       <Provider Name="Microsoft-Windows-Sysmon" Guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}" />
#       <EventID>14</EventID>
#       <Version>2</Version>
#       <Level>4</Level>
#       <Task>14</Task>
#       <Opcode>0</Opcode>
#       <Keywords>0x8000000000000000</Keywords>
#       <TimeCreated SystemTime="2017-05-11T04:38:50.499965200Z" />
#       <EventRecordID>725980</EventRecordID>
#       <Correlation />
#       <Execution ProcessID="3188" ThreadID="3836" />
#       <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#       <Computer>rfsH.lab.local</Computer>
#       <Security UserID="S-1-5-18" />
#   </System>
#   <EventData>
#       <Data Name="EventType">RenameKey</Data>
#       <Data Name="UtcTime">2017-05-11 04:38:50.499</Data>
#       <Data Name="ProcessGuid">{A23EAE89-E8BF-5913-0000-0010DB9F7109}</Data>
#       <Data Name="ProcessId">25228</Data>
#       <Data Name="Image">C:\Windows\regedit.exe</Data>
#       <Data Name="TargetObject">\REGISTRY\MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce\asdf</Data>
#       <Data Name="NewName">\REGISTRY\MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce\BadWolf</Data>
#   </EventData>
# </Event>
