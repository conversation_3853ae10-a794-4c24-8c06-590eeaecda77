Author: <PERSON>
Description: Device driver error
EventId: 400
Channel: "Microsoft-Windows-Kernel-PnP/Configuration"
Provider: "Microsoft-Windows-Kernel-PnP"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "MatchingDeviceId: %MatchingDeviceId%"
    Values:
      -
        Name: MatchingDeviceId
        Value: "/Event/EventData/Data[@Name=\"MatchingDeviceId\"]"
  -
    Property: PayloadData2
    PropertyValue: "DriverSection: %DriverSection%"
    Values:
      -
        Name: DriverSection
        Value: "/Event/EventData/Data[@Name=\"DriverSection\"]"
  -
    Property: PayloadData3
    PropertyValue: "DriverProvider: %DriverProvider%"
    Values:
      -
        Name: DriverProvider
        Value: "/Event/EventData/Data[@Name=\"DriverProvider\"]"
  -
    Property: PayloadData4
    PropertyValue: "DeviceUpdated: %DeviceUpdated%"
    Values:
      -
        Name: DeviceUpdated
        Value: "/Event/EventData/Data[@Name=\"DeviceUpdated\"]"
  -
    Property: PayloadData5
    PropertyValue: "ParentDeviceInstanceId: %ParentDeviceInstanceId%"
    Values:
      -
        Name: ParentDeviceInstanceId
        Value: "/Event/EventData/Data[@Name=\"ParentDeviceInstanceId\"]"
  -
    Property: PayloadData6
    PropertyValue: "DeviceInstanceID: %DeviceInstanceID%"
    Values:
      -
        Name: DeviceInstanceID
        Value: "/Event/EventData/Data[@Name=\"DeviceInstanceID\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%DriverName%"
    Values:
      -
        Name: DriverName
        Value: "/Event/EventData/Data[@Name=\"DriverName\"]"

# Documentation:
# https://docs.microsoft.com/en-us/windows-hardware/drivers/install/driver-rank-ranges--windows-vista-and-later-
# https://www.eventid.net/displayqueue.asp?eventid=400
# https://forensixchange.com/posts/19_08_03_usb_storage_forensics_1/
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Kernel-PnP" Guid="9c679a39-1250-487d-abd7-e831c6290539" />
#    <EventID>400</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x4000000500000000</Keywords>
#    <TimeCreated SystemTime="2019-06-25 16:54:32.9955521" />
#    <EventRecordID>2811</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="4" ThreadID="4568" />
#    <Channel>Microsoft-Windows-Kernel-PnP/Configuration</Channel>
#    <Computer>HOSTNAME.domain.com</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="DeviceInstanceId">SWD\PRINTENUM\{3CDEEBDB-6F0B-4ECB-94CD-3151F17A3B59}</Data>
#    <Data Name="DriverName">printqueue.inf</Data>
#    <Data Name="ClassGuid">1ed2fff9-11f0-4084-b21f-ad83a8e6dcdc</Data>
#    <Data Name="DriverDate">06/21/2006</Data>
#    <Data Name="DriverVersion">10.0.14393.0</Data>
#    <Data Name="DriverProvider">Microsoft</Data>
#    <Data Name="DriverInbox">True</Data>
#    <Data Name="DriverSection">NO_DRV_LOCAL</Data>
#    <Data Name="DriverRank">0x1</Data>
#    <Data Name="MatchingDeviceId">PRINTENUM\LocalPrintQueue</Data>
#    <Data Name="OutrankedDrivers">oem0.inf:{013f01fa-e634-4d77-83ee-074817c03581}:00FF0002 c_swdevice.inf:SWD\GenericRaw:00FF3001</Data>
#    <Data Name="DeviceUpdated">False</Data>
#    <Data Name="Status">0x0</Data>
#    <Data Name="ParentDeviceInstanceId">SWD\PRINTENUM\PrintQueues</Data>
#  </EventData>
# </Event>
