Author: <PERSON> sa<PERSON><EMAIL>
Description: NTLM authentication request
EventId: 4776
Channel: Security
Maps: 
  - 
    Property: PayloadData1
    PropertyValue: "Target: %TargetUserName%"
    Values: 
      - 
        Name: TargetUserName
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
  - 
    Property: PayloadData2
    PropertyValue: "Workstation: %Workstation%"
    Values: 
      - 
        Name: Workstation
        Value: "/Event/EventData/Data[@Name=\"Workstation\"]"
  - 
    Property: PayloadData3
    PropertyValue: "Status: %Status%"
    Values: 
      - 
        Name: Status
        Value: "/Event/EventData/Data[@Name=\"Status\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
#<Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Security-Auditing" Guid="*************-4994-a5ba-3e3b0328c30d" />
#    <EventID>4776</EventID>
#    <Version>0</Version>
#    <Level>0</Level>
#    <Task>14336</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8010000000000000</Keywords>
#    <TimeCreated SystemTime="2018-07-11 05:42:11.1768331" />
#    <EventRecordID>24410</EventRecordID>
#    <Correlation ActivityID="3fd16c43-fc41-0001-ad6c-d13f41fcd301" />
#    <Execution ProcessID="744" ThreadID="2756" />
#    <Channel>Security</Channel>
#    <Computer>base-rd-01.shieldbase.lan</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="PackageName">MICROSOFT_AUTHENTICATION_PACKAGE_V1_0</Data>
#    <Data Name="TargetUserName">tdungan</Data>
#    <Data Name="Workstation">BASE-RD-01</Data>
#    <Data Name="Status">0xC0000064</Data>
#  </EventData>
#</Event>