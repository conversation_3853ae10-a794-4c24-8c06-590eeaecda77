Author: <PERSON>
Description: System security access was granted to an account
EventId: 4717
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user% (%sid%)"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"SubjectUserSid\"]"
  -
    Property: PayloadData1
    PropertyValue: "TargetSID: %sid%"
    Values:
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"TargetSid\"]"
  -
    Property: PayloadData2
    PropertyValue: "AccessGranted: %AccessGranted%"
    Values:
      -
        Name: AccessGranted
        Value: "/Event/EventData/Data[@Name=\"AccessGranted\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4717
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4717
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-A5BA-3E3B0328C30D}" />
# <EventID>4717</EventID>
# <Version>0</Version>
# <Level>0</Level>
# <Task>13569</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8020000000000000</Keywords>
# <TimeCreated SystemTime="2015-10-02T00:02:33.213572000Z" />
# <EventRecordID>1049777</EventRecordID>
# <Correlation />
# <Execution ProcessID="500" ThreadID="2064" />
# <Channel>Security</Channel>
# <Computer>DC01.contoso.local</Computer>
# <Security />
# </System>
# <EventData>
# <Data Name="SubjectUserSid">S-1-5-18</Data>
# <Data Name="SubjectUserName">DC01$</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x3e7</Data>
# <Data Name="TargetSid">S-1-5-21-**********-**********-823803824-2104</Data>
# <Data Name="AccessGranted">SeInteractiveLogonRight</Data>
# </EventData>
# </Event>
