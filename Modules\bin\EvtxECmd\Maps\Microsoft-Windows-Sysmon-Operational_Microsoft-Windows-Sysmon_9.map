Author: <PERSON>
Description: RawAccessRead
EventId: 9
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:
  -
    Property: PayloadData1
    PropertyValue: "ProcessID: %ProcessID%, ProcessGUID: %ProcessGUID%"
    Values:
      -
        Name: ProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ProcessGuid\"]"
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData2
    PropertyValue: "Device: %Device%"
    Values:
      -
        Name: Device
        Value: "/Event/EventData/Data[@Name=\"Device\"]"
  -
    Property: PayloadData3
    PropertyValue: "Image: %Image%"
    Values:
      -
        Name: Image
        Value: "/Event/EventData/Data[@Name=\"Image\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
#   <Provider Name="Microsoft-Windows-Sysmon" Guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}" />
#   <EventID>9</EventID>
#   <Version>2</Version>
#   <Level>4</Level>
#   <Task>9</Task>
#   <Opcode>0</Opcode>
#   <Keywords>0x8000000000000000</Keywords>
#   <TimeCreated SystemTime="2018-03-22T20:32:22.333778700Z" />
#   <EventRecordID>1944686</EventRecordID>
#   <Correlation />
#   <Execution ProcessID="19572" ThreadID="21888" />
#   <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#   <Computer>rfsH.lab.local</Computer>
#   <Security UserID="S-1-5-18" />
# </System>
# <EventData>
#   <Data Name="UtcTime">2018-03-22 20:32:22.332</Data>
#   <Data Name="ProcessGuid">{A23EAE89-C65F-5AB2-0000-0010EB030000}</Data>
#   <Data Name="ProcessId">4</Data>
#   <Data Name="Image">System</Data>
#   <Data Name="Device">\Device\HarddiskVolume2</Data>
# </EventData>
# </Event>
