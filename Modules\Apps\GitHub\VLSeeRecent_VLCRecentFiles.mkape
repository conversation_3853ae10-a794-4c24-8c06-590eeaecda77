Description: 'VLC Recent Files Parser'
Category: FileKnowledge
Author: <PERSON>
Version: 1.0
Id: 43632314-7e65-4730-91ac-b46e45487468
BinaryUrl: https://github.com/northloopforensics/VL_See_Recent/releases
ExportFormat: txt
Processors:
    -
        Executable: VL_See_Recent.exe
        CommandLine: "%sourceDirectory% %destinationDirectory%"
        ExportFormat: txt

# Documentation
# https://github.com/northloopforensics/VL_See_Recent
# Executable to parse recent file activity from the VLC vlc-qt-interface.ini file.
# This program was written for use on Kroll's KAPE tool. Download the release and then copy the #executable to: kape\Modules\bin
# The tool parses: The file patch for each user's ini file and their recent play history.
