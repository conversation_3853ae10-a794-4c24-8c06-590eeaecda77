Author: <PERSON><PERSON> <PERSON> @hyuunnn
Description: WIFI connection was terminated
EventId: 8003
Channel: "Microsoft-Windows-WLAN-AutoConfig/Operational"
Provider: Microsoft-Windows-WLAN-AutoConfig
Maps:
  -
    Property: PayloadData1
    PropertyValue: "ConnectionMode: %ConnectionMode%"
    Values:
      -
        Name: ConnectionMode
        Value: "/Event/EventData/Data[@Name=\"ConnectionMode\"]"
  -
    Property: PayloadData2
    PropertyValue: "ProfileName: %ProfileName%"
    Values:
      -
        Name: ProfileName
        Value: "/Event/EventData/Data[@Name=\"ProfileName\"]"
  -
    Property: PayloadData3
    PropertyValue: "SSID: %SSID%"
    Values:
      -
        Name: SSID
        Value: "/Event/EventData/Data[@Name=\"SSID\"]"
  -
    Property: PayloadData4
    PropertyValue: "BSSType: %BSSType%"
    Values:
      -
        Name: BSSType
        Value: "/Event/EventData/Data[@Name=\"BSSType\"]"
  -
    Property: PayloadData5
    PropertyValue: "Reason: %Reason%"
    Values:
      -
        Name: Reason
        Value: "/Event/EventData/Data[@Name=\"Reason\"]"

# Documentation:
# https://www.sans.org/blog/making-the-most-out-of-wlan-event-log-artifacts/
# https://nasbench.medium.com/finding-forensic-goodness-in-obscure-windows-event-logs-60e978ea45a3
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#    <System>
#        <Provider Name="Microsoft-Windows-WLAN-AutoConfig" Guid="{GUID}" />
#        <EventID>8003</EventID>
#        <Version>0</Version>
#        <Level>4</Level>
#        <Task>24010</Task>
#        <Opcode>192</Opcode>
#        <Keywords>0x8000000040000600</Keywords>
#        <TimeCreated SystemTime="2020-12-02T23:01:09.8177071Z" />
#        <EventRecordID>16</EventRecordID>
#        <Correlation />
#        <Execution ProcessID="3352" ThreadID="3520" />
#        <Channel>Microsoft-Windows-WLAN-AutoConfig/Operational</Channel>
#        <Computer>ComputerName</Computer>
#        <Security UserID="S-1-5-18" />
#    </System>
#    <EventData>
#        <Data Name="InterfaceGuid">{InterfaceGuid}</Data>
#        <Data Name="InterfaceDescription">Intel(R) Dual Band Wireless-AC 7265</Data>
#        <Data Name="ConnectionMode">{ConnectionMode}</Data>
#        <Data Name="ProfileName">asdf</Data>
#        <Data Name="SSID">asdf</Data>
#        <Data Name="BSSType">Infrastructure</Data>
#        <Data Name="Reason">{Reason}</Data>
#        <Data Name="ConnectionId">0x3</Data>
#        <Data Name="ReasonCode">2</Data>
#    </EventData>
# </Event>
