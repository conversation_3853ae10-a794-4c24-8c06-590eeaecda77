Author: <PERSON>
Description: Proxy disconnection
EventId: 5172
Channel: Varonis
Provider: "VrnsCifsQueue"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%Data%"
    Values:
      -
        Name: Data
        Value: "/Event/EventData/Data"

# Documentation:
# There is no public documentation on these events. Varonis is a data security platform so some file system activity appears to be tracked by it.
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="VrnsCifsQueue" />
#    <EventID Qualifiers="0">5172</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000330000000</Keywords>
#    <TimeCreated SystemTime="2020-09-15 18:32:21.0000000" />
#    <EventRecordID>195</EventRecordID>
#    <Channel>Varonis</Channel>
#    <Computer>HOSTNAME1.domain.com</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>VrnsCifsQueue, 1234, 5678, [HOSTNAME1.domain.com]: DisconnectFiler(filerID[57] filerType[ProxyLocal]) request from Probe[HOSTNAME2-02:1234] IP[**********] is successfully performed</Data>
#    <Binary></Binary>
#  </EventData>
# </Event>
