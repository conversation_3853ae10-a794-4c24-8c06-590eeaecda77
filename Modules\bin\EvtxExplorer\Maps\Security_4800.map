Author: <PERSON><PERSON> <EMAIL>
Description: The workstation was locked
EventId: 4800
Channel: Security
Maps: 
  - 
    Property: PayloadData1
    PropertyValue: "Target: %TargetDomainName%\\%TargetUserName%"
    Values: 
      - 
        Name: TargetDomainName
        Value: "/Event/EventData/Data[@Name=\"TargetDomainName\"]"
      - 
        Name: TargetUserName
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
# <Event>
#   <System>
#       <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-a5ba-3e3b0328c30d}" /> 
#       <EventID>4800</EventID> 
#       <Version>0</Version> 
#       <Level>0</Level> 
#       <Task>12551</Task> 
#       <Opcode>0</Opcode> 
#       <Keywords>0x8020000000000000</Keywords> 
#       <TimeCreated SystemTime="2019-04-27T21:23:49.758740900Z" /> 
#       <EventRecordID>90525</EventRecordID> 
#       <Correlation ActivityID="{32dfcbe2-f43e-0002-74cc-df323ef4d401}" /> 
#       <Execution ProcessID="940" ThreadID="11368" /> 
#       <Channel>Security</Channel> 
#       <Computer>pooh</Computer> 
#       <Security /> 
# </System>
#   <EventData>
#       <Data Name="TargetUserSid">S-1-5-21-**********-730768085-**********-1001</Data> 
#       <Data Name="TargetUserName">baz</Data> 
#       <Data Name="TargetDomainName">POOH</Data> 
#       <Data Name="TargetLogonId">0x1887d9f9</Data> 
#       <Data Name="SessionId">19</Data> 
#   </EventData>
# </Event>