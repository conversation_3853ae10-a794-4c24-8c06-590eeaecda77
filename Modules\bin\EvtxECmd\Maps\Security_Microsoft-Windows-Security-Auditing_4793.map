Author: <PERSON>
Description: The Password Policy Checking API was called
EventId: 4793
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user% (%sid%)"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
      -
        Name: sid
        Value: "/Event/EventData/Data[@Name=\"SubjectUserSid\"]"
  -
    Property: PayloadData1
    PropertyValue: "Target: %TargetUserName%"
    Values:
      -
        Name: TargetUserName
        Value: "/Event/EventData/Data[@Name=\"TargetUserName\"]"
  -
    Property: PayloadData2
    PropertyValue: "Workstation: %Workstation%"
    Values:
      -
        Name: Workstation
        Value: "/Event/EventData/Data[@Name=\"Workstation\"]"
  -
    Property: PayloadData3
    PropertyValue: "Status: %Status%"
    Values:
      -
        Name: Status
        Value: "/Event/EventData/Data[@Name=\"Status\"]"
  -
    Property: PayloadData4
    PropertyValue: "SubjectLogonId: %SubjectLogonId%"
    Values:
      -
        Name: SubjectLogonId
        Value: "/Event/EventData/Data[@Name=\"SubjectLogonId\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4793
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4793
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-A5BA-3E3B0328C30D}" />
# <EventID>4793</EventID>
# <Version>0</Version>
# <Level>0</Level>
# <Task>13829</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8020000000000000</Keywords>
# <TimeCreated SystemTime="2015-08-18T02:37:46.322424300Z" />
# <EventRecordID>172342</EventRecordID>
# <Correlation />
# <Execution ProcessID="520" ThreadID="2964" />
# <Channel>Security</Channel>
# <Computer>DC01.contoso.local</Computer>
# <Security />
# </System>
# <EventData>
# <Data Name="SubjectUserSid">S-1-5-21-**********-**********-823803824-1104</Data>
# <Data Name="SubjectUserName">dadmin</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x36f67</Data>
# <Data Name="Workstation">DC01</Data>
# <Data Name="TargetUserName">-</Data>
# <Data Name="Status">0x0</Data>
# </EventData>
# </Event>
