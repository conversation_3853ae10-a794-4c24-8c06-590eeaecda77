Author: <PERSON>
Description:  An attempt to exploit a known vulnerability detected.
EventId: 2
Channel: "System"
Maps: 
  - 
    Property: PayloadData1
    PropertyValue: "%CVEID%"
    Values: 
      - 
        Name: CVEID
        Value: "/Event/EventData/Data[@Name=\"CVEID\"]"
  - 
    Property: PayloadData2
    PropertyValue: "%AdditionalDetails%"
    Values: 
      - 
        Name: AdditionalDetails
        Value: "/Event/EventData/Data[@Name=\"AdditionalDetails\"]"
        
# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

#Sample Event - derived from the event template.
#<System>
#  <Provider Name="Microsoft-Windows-Audit-CVE" Guid="{85a62a0d-7e17-485f-9d4f-749a287193a6}" /> 
#  <EventID>2</EventID> 
#  <Version>0</Version> 
#  <Level>3</Level> 
#  <Task>0</Task> 
#  <Opcode>0</Opcode> 
#  <Keywords>0x8000000000000000</Keywords> 
#  <TimeCreated SystemTime="2020-01-15T23:53:36.821170000Z" /> 
#  <EventRecordID>32459</EventRecordID> 
#  <Correlation /> 
#  <Execution ProcessID="19928" ThreadID="15488" /> 
#  <Channel>System</Channel> 
#  <Computer>xxxxxxxxxxxxxxxxxx</Computer> 
#  <Security UserID="xxxxxxxxxxxxxxxxxxxxxxxxx" /> 
#</System>
#<EventData>
#  <Data Name="CVEID">xxxxxxxxxxxxxxxxx</Data> 
#  <Data Name="AdditionalDetails">xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</Data> 
#</EventData>
