Author: <PERSON> sa<PERSON><EMAIL>
Description: "Remote Desktop Services: Shell start notification received"
EventId: 22
Channel: Microsoft-Windows-TerminalServices-LocalSessionManager/Operational
Provider: Microsoft-Windows-TerminalServices-LocalSessionManager
Maps:
  -
    Property: UserName
    PropertyValue: "%User%"
    Values:
      -
        Name: User
        Value: "/Event/UserData/EventXML/User"
  -
    Property: RemoteHost
    PropertyValue: "%Address%"
    Values:
      -
        Name: Address
        Value: "/Event/UserData/EventXML/Address"

# Documentation:
# https://ponderthebits.com/2018/02/windows-rdp-related-event-logs-identification-tracking-and-investigation/
# https://www.13cubed.com/downloads/rdp_flowchart.pdf
# https://dfironthemountain.wordpress.com/2019/02/15/rdp-event-log-dfir/
# http://woshub.com/rdp-connection-logs-forensics-windows/
# https://countuponsecurity.com/2015/11/25/digital-forensics-supertimeline-event-logs-part-ii/
# https://frsecure.com/blog/rdp-connection-event-logs/
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-TerminalServices-LocalSessionManager" Guid="5d896912-022d-40aa-a3a8-4fa5515c76d7" />
#    <EventID>22</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x1000000000000000</Keywords>
#    <TimeCreated SystemTime="2018-08-06 19:22:30.5070915" />
#    <EventRecordID>275</EventRecordID>
#    <Correlation ActivityID="61a55000-55e5-1017-0000-000000000000" />
#    <Execution ProcessID="864" ThreadID="3640" />
#    <Channel>Microsoft-Windows-TerminalServices-LocalSessionManager/Operational</Channel>
#    <Computer>base-rd-01.shieldbase.lan</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <UserData>
#    <EventXML>
#      <User>shieldbase\tdungan</User>
#      <SessionID>1</SessionID>
#      <Address>LOCAL</Address>
#    </EventXML>
#  </UserData>
# </Event>
