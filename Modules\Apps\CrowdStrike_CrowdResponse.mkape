Description: CrowdResponse is a lightweight Windows console application designed to aid in the gathering of system information for incident response and security engagements
Category: LiveResponse
Author: <PERSON>, <PERSON>
Version: 1.0
Id: e137a1ed-5bfd-4ca3-9df1-c1d35624cbb2
BinaryUrl: https://www.crowdstrike.com/wp-content/uploads/2020/03/CrowdResponse.zip
ExportFormat: csv
Processors:
    -
        Executable: C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe
        CommandLine: -Command "& '%kapedirectory%\Modules\bin\CrowdResponse\CrowdResponse.exe' -i %kapedirectory%\Modules\bin\CrowdResponse\config.txt -o %destinationDirectory%\CrowdResponse.xml;&'%kapedirectory%\Modules\bin\CrowdResponse\CRConvert.exe' -v -c -f %destinationDirectory%\CrowdResponse.xml -o %destinationDirectory%;Remove-Item '%destinationDirectory%\CrowdResponse.xml'"
        ExportFormat: csv

# Documentation
# https://www.crowdstrike.com/resources/community-tools/crowdresponse/
# Create a folder "CrowdResponse" within the "Modules\bin" KAPE folder
# Place the "CrowdResponse.zip" archive into "Modules\bin\CrowdResponse" and extract.
