@ECHO OFF
@setlocal enableextensions enabledelayedexpansion

:::-------------------------------------------------------------------------------------------
:::     __ __ ___    ____  ______   __________  __    __    __________________________  _   __
:::    / //_//   |  / __ \/ ____/  / ____/ __ \/ /   / /   / ____/ ____/_  __/  _/ __ \/ | / /
:::   / ,<  / /| | / /_/ / __/    / /   / / / / /   / /   / __/ / /     / /  / // / / /  |/ / 
:::  / /| |/ ___ |/ ____/ /___   / /___/ /_/ / /___/ /___/ /___/ /___  / / _/ // /_/ / /|  /  
::: /_/ |_/_/  |_/_/   /_____/   \____/\____/_____/_____/_____/\____/ /_/ /___/\____/_/ |_/   
:::
:::-------------------------------------------------------------------------------------------
for /f "delims=: tokens=*" %%A in ('findstr /b ::: "%~f0"') do @echo(%%A

ECHO The KAPE Collection Tool provides the option to:
ECHO 1. Collect specific drive (user selectable)
ECHO 2. Iterate all available drives, except C:/ and Triage destination drive (A:/ to Z:/)
ECHO 3. Iterate all available drives, except Triage destnation drive (A:/ to Z:/)
ECHO.
goto start
:start
set choice=
set /p choice=Type the number to indicate your choice (1, 2 or 3):
if not '%choice%'=='' set choice=%choice:~0,1%
if '%choice%'=='1' goto single_drive
if '%choice%'=='2' goto partial
if '%choice%'=='3' goto all
ECHO Error: Choice %choice% is not valid, please enter your choice again
ECHO.
ECHO.



:single_drive
set drive_letter=
set /p drive_letter=Enter the drive letter you want to collect (e.g., C, D, E, F):
if "%drive_letter%"=="" (
    echo Error: No drive letter entered. Please try again.
    goto single_drive
)
:: Convert to uppercase and validate
set drive_letter=%drive_letter:~0,1%
call :toupper drive_letter
:: Check if drive exists
if not exist %drive_letter%:\ (
    echo Error: Drive %drive_letter%:\ does not exist. Please enter a valid drive letter.
    goto single_drive
)
echo Running KAPE on %drive_letter%: drive..
set /p memory_choice=Do you want to collect memory? [y/[n]]:
if not "%memory_choice%"=="" set memory_choice=%memory_choice:~0,1%
if /i "%memory_choice%"=="y" (
    echo Memory collection enabled..
    call kape_target.bat %drive_letter%
    call kape_module_live_memory.bat %drive_letter%
    goto end
) else if /i "%memory_choice%"=="n" (
    echo Memory collection not enabled..
    call kape_target.bat %drive_letter%
    call kape_module_live.bat %drive_letter%
    goto end
) else (
    echo Error: Choice "%memory_choice%" is not valid, please enter your choice again.
    goto single_drive
)



:partial
	echo Locating all drives..
	set current=%CD:~0,2%
	set /p memory= Do you want to collect memory? [y/[n]]: 

	where wmic >nul 2>&1
	if %errorlevel% equ 0 (
    	wmic logicaldisk where "DriveType=3" get deviceid | more +1 | findstr "." > local_parts.txt
	) else (
    	powershell -NoProfile -Command "Get-CimInstance Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 -and $_.DeviceID -ne 'C:' } | ForEach-Object { $_.DeviceID -replace ':' }" > local_parts.txt
	)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (echo Drive Located: %%a)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (
	set tempDrive=%%a
	set fileDrive=!tempDrive:~0,2!
	if NOT "!current!" == "C:" (
		echo Drive that KAPE is running from: !Current!, will skip this drive..
	if NOT "!current!"=="!fileDrive!" (
		if "!fileDrive!"=="C:" (
			set current=%CD:~0,2%
			echo Running KAPE on C: drive
			if '%memory%' == 'y' ( 
			echo Memory collection enabled..
			call kape_target.bat c
			call kape_module_live_memory.bat c
			) else (
			echo Memory collection not enabled..
			call kape_target.bat c
			call kape_module_live.bat c
			) 
		) else (
			set drive=!fileDrive:~0,1!
			echo Running KAPE on !drive!
			call kape_target.bat !drive!
			call kape_module_file_listing.bat !drive!
		)
		) else (echo Skipping drive that KAPE is ran from: !Current!)) else (
			echo KAPE is located on C: drive..
			if "!fileDrive!"=="C:" (
			echo Running KAPE on C: drive
			set drive=!fileDrive:~0,1!
			call kape_target.bat !drive!
			call kape_module_live.bat !drive!
			) else (
			set drive=!fileDrive:~0,1!
			echo Running KAPE on !drive!
			call kape_target.bat !drive!
			call kape_module_file_listing.bat !drive!
	)
	
		))
	endlocal
goto end

:all
	echo Locating all drives..
	set /p memory= Do you want to collect memory? [y/[n]]: 
	set current=%CD:~0,2%
	echo KAPE is located on !current! drive..
	where wmic >nul 2>&1
	if %errorlevel% equ 0 (
    	wmic logicaldisk where "DriveType=3" get deviceid | more +1 | findstr "." > local_parts.txt
	) else (
    	powershell -NoProfile -Command "Get-CimInstance Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 -and $_.DeviceID -ne 'C:' } | ForEach-Object { $_.DeviceID -replace ':' }" > local_parts.txt
	)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (echo Drive Located: %%a)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (
	set tempDrive=%%a
	set drive=!tempDrive:~0,1!
	echo Running KAPE on !drive! drive
	if "!drive!" == "C" (
		if '%memory%' == 'y' ( 
			echo Memory collection enabled..
			call kape_target.bat c
			call kape_module_live_memory.bat c
			) else (
			echo Memory collection not enabled..
			call kape_target.bat c
			call kape_module_live.bat c
			)) else (
	call kape_target.bat !drive!
	call kape_module_file_listing.bat !drive!
	)
	)
goto end	

:end
goto :eof

:toupper
:: Convert variable to uppercase
setlocal enabledelayedexpansion
set "str=!%~1!"
set "str=!str:a=A!"
set "str=!str:b=B!"
set "str=!str:c=C!"
set "str=!str:d=D!"
set "str=!str:e=E!"
set "str=!str:f=F!"
set "str=!str:g=G!"
set "str=!str:h=H!"
set "str=!str:i=I!"
set "str=!str:j=J!"
set "str=!str:k=K!"
set "str=!str:l=L!"
set "str=!str:m=M!"
set "str=!str:n=N!"
set "str=!str:o=O!"
set "str=!str:p=P!"
set "str=!str:q=Q!"
set "str=!str:r=R!"
set "str=!str:s=S!"
set "str=!str:t=T!"
set "str=!str:u=U!"
set "str=!str:v=V!"
set "str=!str:w=W!"
set "str=!str:x=X!"
set "str=!str:y=Y!"
set "str=!str:z=Z!"
endlocal & set "%~1=%str%"
goto :eof