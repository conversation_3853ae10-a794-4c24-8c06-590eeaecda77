@ECHO OFF
@setlocal enableextensions enabledelayedexpansion

:::-------------------------------------------------------------------------------------------
:::     __ __ ___    ____  ______   __________  __    __    __________________________  _   __
:::    / //_//   |  / __ \/ ____/  / ____/ __ \/ /   / /   / ____/ ____/_  __/  _/ __ \/ | / /
:::   / ,<  / /| | / /_/ / __/    / /   / / / / /   / /   / __/ / /     / /  / // / / /  |/ / 
:::  / /| |/ ___ |/ ____/ /___   / /___/ /_/ / /___/ /___/ /___/ /___  / / _/ // /_/ / /|  /  
::: /_/ |_/_/  |_/_/   /_____/   \____/\____/_____/_____/_____/\____/ /_/ /___/\____/_/ |_/   
:::
:::-------------------------------------------------------------------------------------------
for /f "delims=: tokens=*" %%A in ('findstr /b ::: "%~f0"') do @echo(%%A

ECHO The KAPE Collection Tool provides the option to:
ECHO 1. Collect only E:/ drive
ECHO 2. Iterate all available drives, except C:/ and Triage destination drive (A:/ to Z:/)
ECHO 3. Iterate all available drives, except Triage destnation drive (A:/ to Z:/)
ECHO.
goto start
:start
set choice=
set /p choice=Type the number to indicate your choice (1, 2 or 3): 
if not '%choice%'=='' set choice=%choice:~0,1%
if '%choice%'=='1' goto f
if '%choice%'=='2' goto partial
if '%choice%'=='3' goto all
ECHO Error: Choice %choice% is not valid, please enter your choice again
ECHO.
ECHO.



:f
echo Running KAPE on only F drive..
set /p choice=Do you want to collect memory? [y/[n]]: 
if not "%choice%"=="" set choice=%choice:~0,1%
if /i "%choice%"=="y" (
    echo Memory collection enabled..
    call kape_target.bat f
    call kape_module_live_memory.bat f
    goto end
) else if /i "%choice%"=="n" (
    echo Memory collection not enabled..
    call kape_target.bat f
    call kape_module_live.bat f
    goto end
) else (
    echo Error: Choice "%choice%" is not valid, please enter your choice again.
    goto f
)



:partial
	echo Locating all drives..
	set current=%CD:~0,2%
	set /p memory= Do you want to collect memory? [y/[n]]: 

	where wmic >nul 2>&1
	if %errorlevel% equ 0 (
    	wmic logicaldisk where "DriveType=3" get deviceid | more +1 | findstr "." > local_parts.txt
	) else (
    	powershell -NoProfile -Command "Get-CimInstance Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 -and $_.DeviceID -ne 'C:' } | ForEach-Object { $_.DeviceID -replace ':' }" > local_parts.txt
	)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (echo Drive Located: %%a)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (
	set tempDrive=%%a
	set fileDrive=!tempDrive:~0,2!
	if NOT "!current!" == "C:" (
		echo Drive that KAPE is running from: !Current!, will skip this drive..
	if NOT "!current!"=="!fileDrive!" (
		if "!fileDrive!"=="C:" (
			set current=%CD:~0,2%
			echo Running KAPE on C: drive
			if '%memory%' == 'y' ( 
			echo Memory collection enabled..
			call kape_target.bat c
			call kape_module_live_memory.bat c
			) else (
			echo Memory collection not enabled..
			call kape_target.bat c
			call kape_module_live.bat c
			) 
		) else (
			set drive=!fileDrive:~0,1!
			echo Running KAPE on !drive!
			call kape_target.bat !drive!
			call kape_module_file_listing.bat !drive!
		)
		) else (echo Skipping drive that KAPE is ran from: !Current!)) else (
			echo KAPE is located on C: drive..
			if "!fileDrive!"=="C:" (
			echo Running KAPE on C: drive
			set drive=!fileDrive:~0,1!
			call kape_target.bat !drive!
			call kape_module_live.bat !drive!
			) else (
			set drive=!fileDrive:~0,1!
			echo Running KAPE on !drive!
			call kape_target.bat !drive!
			call kape_module_file_listing.bat !drive!
	)
	
		))
	endlocal
goto end

:all
	echo Locating all drives..
	set /p memory= Do you want to collect memory? [y/[n]]: 
	set current=%CD:~0,2%
	echo KAPE is located on !current! drive..
	where wmic >nul 2>&1
	if %errorlevel% equ 0 (
    	wmic logicaldisk where "DriveType=3" get deviceid | more +1 | findstr "." > local_parts.txt
	) else (
    	powershell -NoProfile -Command "Get-CimInstance Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 -and $_.DeviceID -ne 'C:' } | ForEach-Object { $_.DeviceID -replace ':' }" > local_parts.txt
	)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (echo Drive Located: %%a)
	FOR /F "tokens=*" %%a in ('"type local_parts.txt | find /v """') do (
	set tempDrive=%%a
	set drive=!tempDrive:~0,1!
	echo Running KAPE on !drive! drive
	if "!drive!" == "C" (
		if '%memory%' == 'y' ( 
			echo Memory collection enabled..
			call kape_target.bat c
			call kape_module_live_memory.bat c
			) else (
			echo Memory collection not enabled..
			call kape_target.bat c
			call kape_module_live.bat c
			)) else (
	call kape_target.bat !drive!
	call kape_module_file_listing.bat !drive!
	)
	)
goto end	

:end