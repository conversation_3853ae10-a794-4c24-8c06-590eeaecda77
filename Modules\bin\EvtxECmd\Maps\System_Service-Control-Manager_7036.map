Author: <PERSON> sa<PERSON><EMAIL>
Description: Service started or stopped
EventId: 7036
Channel: System
Provider: Service Control Manager
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Name: %ServiceName% | %ServiceName2%"
    Values:
      -
        Name: ServiceName
        Value: "/Event/EventData/Data[@Name=\"param1\"]"
      -
        Name: ServiceName2
        Value: "/Event/EventData/Data"
  -
    Property: PayloadData2
    PropertyValue: "Status: %Status%"
    Values:
      -
        Name: Status
        Value: "/Event/EventData/Data[@Name=\"param2\"]"

# Documentation:
# https://docs.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2008-R2-and-2008/cc756308(v=ws.10)?redirectedfrom=MSDN
# https://kb.eventtracker.com/evtpass/evtPages/EventId_7036_ServiceControlManager_46021.asp
# https://morgantechspace.com/2013/10/event-id-7036-service-entered-stopped.html
# In regards to PayloadData1, this is a special case in that data may exist in several forms. Here we look for both and use the one we find. =)
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Service Control Manager" Guid="{555908d1-a6d7-4695-8e1e-26931d2012f4}" EventSourceName="Service Control Manager" />
#    <EventID Qualifiers="16384">7036</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8080000000000000</Keywords>
#    <TimeCreated SystemTime="2012-03-13 20:11:22.3763130" />
#    <EventRecordID>11894</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="560" ThreadID="3280" />
#    <Channel>System</Channel>
#    <Computer>WKS-WIN732BITA.shieldbase.local</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="param1">Multimedia Class Scheduler</Data>
#    <Data Name="param2">running</Data>
#    <Binary>4D-00-4D-00-43-00-53-00-53-00-2F-00-34-00-00-00</Binary>
#  </EventData>
# </Event>
#
# <Event>
#  <System>
#    <Provider Name="Netwtw06" />
#    <EventID Qualifiers="16384">7036</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000000000000</Keywords>
#    <TimeCreated SystemTime="2018-12-23 00:23:18.7175960" />
#    <EventRecordID>696</EventRecordID>
#    <Channel>System</Channel>
#    <Computer>DESKTOP-9L1HKC9</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>\Device\NDMP1, Intel(R) Dual Band Wireless-AC 8265</Data>
#    <Binary>00-00-08-00-02-00-38-00-00-00-00-00-7C-1B-00-40-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-00-57-44-52-56-AB-00-00-00</Binary>
#  </EventData>
# </Event>
