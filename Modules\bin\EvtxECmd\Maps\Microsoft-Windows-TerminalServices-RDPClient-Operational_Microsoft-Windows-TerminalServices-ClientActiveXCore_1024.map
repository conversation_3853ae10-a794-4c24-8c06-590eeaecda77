Author: <PERSON> sa<PERSON><EMAIL>
Description: RDP Client is trying to connect to the server
EventId: 1024
Channel: Microsoft-Windows-TerminalServices-RDPClient/Operational
Provider: Microsoft-Windows-TerminalServices-ClientActiveXCore
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Dest: %DestServer%"
    Values:
      -
        Name: DestServer
        Value: "/Event/EventData/Data[@Name=\"Value\"]"
  -
    Property: PayloadData6
    PropertyValue: "ActivityID: %ActivityID%"
    Values:
      -
        Name: ActivityID
        Value: "/Event/System/Correlation/@ActivityID"

# Documentation:
# RDP ClientActiveX is trying to connect to the server ([Destination Host Name]).
# https://cyber-tls.blogspot.com/2019/08/rdp.html
# https://social.technet.microsoft.com/wiki/contents/articles/37847.rdp-direct-connection-with-nla-remote-desktop-client-event-logs.aspx
# https://nullsec.us/windows-rdp-related-event-logs-the-client-side-of-the-story/
# https://frsecure.com/blog/rdp-connection-event-logs/
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-TerminalServices-ClientActiveXCore" Guid="28aa95bb-d444-4719-a36f-40462168127e" />
#    <EventID>1024</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>101</Task>
#    <Opcode>10</Opcode>
#    <Keywords>0x4000000000000000</Keywords>
#    <TimeCreated SystemTime="2018-08-31 14:53:31.6512242" />
#    <EventRecordID>1</EventRecordID>
#    <Correlation ActivityID="6e1e588c-e540-4405-b784-16ca89260000" />
#    <Execution ProcessID="11636" ThreadID="10688" />
#    <Channel>Microsoft-Windows-TerminalServices-RDPClient/Operational</Channel>
#    <Computer>base-rd-01.shieldbase.lan</Computer>
#    <Security UserID="S-1-5-21-**********-**********-**********-1193" />
#  </System>
#  <EventData>
#    <Data Name="Name">Server Name</Data>
#    <Data Name="Value">172.16.6.14</Data>
#    <Data Name="CustomLevel">Info</Data>
#  </EventData>
# </Event>
