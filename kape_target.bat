@echo off
setlocal enabledelayedexpansion

:: Get current date and time
set CUR_DATE=%DATE%
set CUR_TIME=%TIME%
set DRIVE=%1

:: Extract weekday string (first few chars)
set CUR_DAY=%CUR_DATE:~0,2%

:: Detect format
if "%CUR_DAY%"=="Mo" (
    set FORMAT=EN
) else if "%CUR_DAY%"=="Tu" (
    set FORMAT=EN
) else if "%CUR_DAY%"=="We" (
    set FORMAT=EN
) else if "%CUR_DAY%"=="Th" (
    set FORMAT=EN
) else if "%CUR_DAY%"=="Fr" (
    set FORMAT=EN
) else if "%CUR_DAY%"=="Sa" (
    set FORMAT=EN
) else if "%CUR_DAY%"=="Su" (
    set FORMAT=EN
) else if "%CUR_DAY%"=="周一" (
    set FORMAT=CN
) else if "%CUR_DAY%"=="周二" (
    set FORMAT=CN
) else if "%CUR_DAY%"=="周三" (
    set FORMAT=CN
) else if "%CUR_DAY%"=="周四" (
    set FORMAT=CN
) else if "%CUR_DAY%"=="周五" (
    set FORMAT=CN
) else if "%CUR_DAY%"=="周六" (
    set FORMAT=CN
) else if "%CUR_DAY%"=="周日" (
    set FORMAT=CN
) else (
    set FORMAT=UNKNOWN
)

:: Parse date based on detected format
if "%FORMAT%"=="EN" (
    echo Detected: English-style date
    set CUR_YYYY=%CUR_DATE:~10,4%
    set CUR_MM=0%CUR_DATE:~4,2%
    set CUR_DD=0%CUR_DATE:~7,2%
) else if "%FORMAT%"=="CN" (
    echo Detected: Chinese-style date
    set CUR_YYYY=%CUR_DATE:~11,4%
    set CUR_MM=0%CUR_DATE:~8,2%
    set CUR_DD=0%CUR_DATE:~5,2%
) else (
    echo WARNING: [TARGETOUTPUT] Unknown date format! Using default values.
    set CUR_YYYY=0000
    set CUR_MM=00
    set CUR_DD=00
)

:: Parse time
set CUR_HH=%CUR_TIME:~0,2%
set CUR_MIN=%CUR_TIME:~3,2%
set CUR_SS=%CUR_TIME:~6,2%

:: Ensure formatting
set CUR_MM=%CUR_MM:~-2%
set CUR_DD=%CUR_DD:~-2%

:: Build folder names
set DIRNAME=%CUR_YYYY%%CUR_MM%%CUR_DD%%CUR_HH%%CUR_MIN%%CUR_SS%_%COMPUTERNAME%_%DRIVE%_
set TARGETOUTPUT="%~dp0%DIRNAME%\%DIRNAME%#KAPE_Target_Output"

echo Final folder: %TARGETOUTPUT%

:: Create directory if it doesn't exist
if not exist "%~dp0%DIRNAME%" (
    mkdir "%~dp0%DIRNAME%"
)

if not exist %TARGETOUTPUT% (
    mkdir %TARGETOUTPUT%
)

:: Run KAPE with target output
set Run_DIR=cd
set KAPE=%~dp0kape.exe
echo Running KAPE for drive %DRIVE%:
start %KAPE% --tsource !DRIVE!^:\ --tdest %TARGETOUTPUT% --tflush --target Browsers,Windows,Logs,Apps,Antivirus --zip "%COMPUTERNAME%_!drive!_"

endlocal