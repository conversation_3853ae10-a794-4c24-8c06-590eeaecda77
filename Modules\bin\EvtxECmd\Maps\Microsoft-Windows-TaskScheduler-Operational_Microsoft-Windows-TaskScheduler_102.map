Author: <PERSON> sa<PERSON><EMAIL>
Description: Scheduled Task completed
EventId: 102
Channel: "Microsoft-Windows-TaskScheduler/Operational"
Provider: Microsoft-Windows-TaskScheduler
Maps:
  -
    Property: UserName
    PropertyValue: "Context: %UserContext%"
    Values:
      -
        Name: UserContext
        Value: "/Event/EventData/Data[@Name=\"UserContext\"]"
  -
    Property: PayloadData1
    PropertyValue: "Task: %TaskName%"
    Values:
      -
        Name: TaskName
        Value: "/Event/EventData/Data[@Name=\"TaskName\"]"
  -
    Property: PayloadData2
    PropertyValue: "Instance Id: %InstanceId%"
    Values:
      -
        Name: InstanceId
        Value: "/Event/EventData/Data[@Name=\"InstanceId\"]"

# Documentation:
# https://kb.eventtracker.com/evtpass/evtpages/EventId_102_Microsoft-Windows-TaskScheduler_61752.asp
# https://mnaoumov.wordpress.com/2014/05/15/task-scheduler-event-ids/
#
# Example Event Data:
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-TaskScheduler" Guid="de7b24ea-73c8-4a09-985d-5bdadcfa9017" />
#     <EventID>102</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>102</Task>
#     <Opcode>2</Opcode>
#     <Keywords>0x8000000000000001</Keywords>
#     <TimeCreated SystemTime="2018-08-26 21:00:16.6203086" />
#     <EventRecordID>193470</EventRecordID>
#     <Correlation ActivityID="b16f3c5f-b58a-4144-8f40-cf3d957a9d12" />
#     <Execution ProcessID="1484" ThreadID="3876" />
#     <Channel>Microsoft-Windows-TaskScheduler/Operational</Channel>
#     <Computer>base-rd-01.shieldbase.lan</Computer>
#     <Security UserID="S-1-5-18" />
#   </System>
#   <EventData Name="TaskSuccessEvent">
#     <Data Name="TaskName">\Microsoft\Windows\Windows Error Reporting\QueueReporting</Data>
#     <Data Name="UserContext">NT AUTHORITY\SYSTEM</Data>
#     <Data Name="InstanceId">b16f3c5f-b58a-4144-8f40-cf3d957a9d12</Data>
#   </EventData>
# </Event>
