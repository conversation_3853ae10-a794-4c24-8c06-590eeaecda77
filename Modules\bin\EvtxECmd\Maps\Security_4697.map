Author: <PERSON> <EMAIL>
Description: A service was installed on the system
EventId: 4697
Channel: Security
Maps: 
  - 
    Property: Username
    PropertyValue: "%domain%\\%user%"
    Values: 
      - 
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      - 
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  - 
    Property: PayloadData1
    PropertyValue: "ServiceName: %ServiceName%"
    Values: 
      - 
        Name: ServiceName
        Value: "/Event/EventData/Data[@Name=\"ServiceName\"]"
  - 
    Property: PayloadData2
    PropertyValue: "ServiceFileName: %ServiceFileName%"
    Values: 
      - 
        Name: ServiceFileName
        Value: "/Event/EventData/Data[@Name=\"ServiceFileName\"]"
  - 
    Property: PayloadData3
    PropertyValue: "ServiceType: %ServiceType%"
    Values: 
      - 
        Name: ServiceType
        Value: "/Event/EventData/Data[@Name=\"ServiceType\"]"
  - 
    Property: PayloadData4
    PropertyValue: "ServiceStartType: %ServiceStartType%"
    Values: 
      - 
        Name: ServiceStartType
        Value: "/Event/EventData/Data[@Name=\"ServiceStartType\"]"
  - 
    Property: PayloadData5
    PropertyValue: "ServiceAccount: %ServiceAccount%"
    Values: 
      - 
        Name: ServiceAccount
        Value: "/Event/EventData/Data[@Name=\"ServiceAccount\"]"

# Valid properties include:
# UserName
# TaskName & TaskContent
#   <EventData>
#     <Data Name="SubjectUserSid">S-1-5-18</Data>
#     <Data Name="SubjectUserName">BASE-RD-01$</Data>
#     <Data Name="SubjectDomainName">shieldbase</Data>
#     <Data Name="SubjectLogonId">0x3E7</Data>
#     <Data Name="ServiceName">CDPUserSvc_6fa0e</Data>
#     <Data Name="ServiceFileName">C:\WINDOWS\system32\svchost.exe -k UnistackSvcGroup</Data>
#     <Data Name="ServiceType">0xE0</Data>
#     <Data Name="ServiceStartType">2</Data>
#     <Data Name="ServiceAccount">LocalSystem</Data>
#   </EventData>
