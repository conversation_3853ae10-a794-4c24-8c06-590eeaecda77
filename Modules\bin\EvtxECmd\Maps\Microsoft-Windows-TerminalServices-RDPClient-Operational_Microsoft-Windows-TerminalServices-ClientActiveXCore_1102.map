Author: <PERSON> sa<PERSON><EMAIL>
Description: RDP client has initiated a multi-transport connection to the server
EventId: 1102
Channel: Microsoft-Windows-TerminalServices-RDPClient/Operational
Provider: Microsoft-Windows-TerminalServices-ClientActiveXCore
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Address: %Address%"
    Values:
      -
        Name: Address
        Value: "/Event/EventData/Data[@Name=\"Value\"]"

# Documentation:
# https://cyber-tls.blogspot.com/2019/08/rdp.html
# https://social.technet.microsoft.com/wiki/contents/articles/37847.rdp-direct-connection-with-nla-remote-desktop-client-event-logs.aspx
# https://nullsec.us/windows-rdp-related-event-logs-the-client-side-of-the-story/
# https://docs.microsoft.com/en-us/openspecs/windows_protocols/ms-rdsod/aa0449fc-9642-4206-90a8-aac72f1b72fd
# https://docs.microsoft.com/en-us/openspecs/windows_protocols/ms-rdsod/aa0449fc-9642-4206-90a8-aac72f1b72fd
# https://frsecure.com/blog/rdp-connection-event-logs/
#
# Example Event Data:
# The client has initiated a multi-transport connection to the server *************.
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-TerminalServices-ClientActiveXCore" Guid="28aa95bb-d444-4719-a36f-40462168127e" />
#    <EventID>1102</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>101</Task>
#    <Opcode>10</Opcode>
#    <Keywords>0x4000000000000000</Keywords>
#    <TimeCreated SystemTime="2018-09-05 11:53:15.6282883" />
#    <EventRecordID>83</EventRecordID>
#    <Correlation ActivityID="a5c05495-8936-44ea-a09b-da190c410000" />
#    <Execution ProcessID="5012" ThreadID="8892" />
#    <Channel>Microsoft-Windows-TerminalServices-RDPClient/Operational</Channel>
#    <Computer>base-rd-01.shieldbase.lan</Computer>
#    <Security UserID="S-1-5-21-**********-**********-**********-1193" />
#  </System>
#  <EventData>
#    <Data Name="Name">ServerAddress</Data>
#    <Data Name="Value">**********</Data>
#    <Data Name="CustomLevel">Info</Data>
#  </EventData>
# </Event>
