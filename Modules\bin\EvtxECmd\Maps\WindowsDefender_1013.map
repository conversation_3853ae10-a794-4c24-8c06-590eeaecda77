Author: <PERSON>
Description: <PERSON>fender removed history of malware.
EventId: 1013
Channel: Microsoft-Windows-Windows Defender/Operational
Maps: 
  - 
    Property: UserName
    PropertyValue: "%Domain%\\%User%"
    Values: 
      - 
        Name: Domain
        Value: "/Event/EventData/Data[@Name=\"Domain\"]"
      - 
        Name: User
        Value: "/Event/EventData/Data[@Name=\"User\"]"
  - 
    Property: PayloadData1
    PropertyValue: "%Timestamp%"
    Values: 
      - 
        Name: Timestamp
        Value: "/Event/EventData/Data[@Name=\"Timestamp\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6
# Example XML for this event:
#<Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Windows Defender" Guid="{11cd958a-c507-4ef3-b3f2-5fd9dfbd2c78}" /> 
#    <EventID>1013</EventID> 
#    <Version>0</Version> 
#    <Level>4</Level> 
#    <Task>0</Task> 
#    <Opcode>0</Opcode> 
#    <Keywords>0x8000000000000000</Keywords> 
#    <TimeCreated SystemTime="2019-08-28T21:52:20.117313300Z" /> 
#    <EventRecordID>30878</EventRecordID> 
#    <Correlation /> 
#    <Execution ProcessID="382" ThreadID="1320" /> 
#    <Channel>Microsoft-Windows-Windows Defender/Operational</Channel> 
#    <Computer>Thunder.cloud.corp.com</Computer> 
#    <Security UserID="S-1-5-18" /> 
#  </System>
#  <EventData>
#    <Data Name="Product Name">%%827</Data> 
#    <Data Name="Product Version">4.18.1909.6</Data> 
#    <Data Name="Timestamp">08/13/2019 2:52:20 PM</Data> 
#    <Data Name="Unused" /> 
#    <Data Name="Unused2" /> 
#    <Data Name="Unused3" /> 
#    <Data Name="Unused4" /> 
#    <Data Name="Domain">NT AUTHORITY</Data> 
#    <Data Name="User">SYSTEM</Data> 
#    <Data Name="SID">S-1-5-18</Data>
#  </EventData>
#</Event>