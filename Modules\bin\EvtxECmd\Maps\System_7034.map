Author: <PERSON> sa<PERSON><EMAIL>
Description: Service crashed unexpectedly
EventId: 7034
Channel: System
Maps: 
  - 
    Property: PayloadData1
    PropertyValue: "Name: %ServiceName%"
    Values: 
      - 
        Name: ServiceName
        Value: "/Event/EventData/Data[@Name=\"param1\"]"


        
# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

#<Event>
#  <System>
#    <Provider Name="Service Control Manager" Guid="{555908d1-a6d7-4695-8e1e-26931d2012f4}" EventSourceName="Service Control Manager" />
#    <EventID Qualifiers="49152">7034</EventID>
#    <Version>0</Version>
#    <Level>2</Level>
#    <Task>0</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8080000000000000</Keywords>
#    <TimeCreated SystemTime="2012-04-04 01:48:01.8866267" />
#    <EventRecordID>15001</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="548" ThreadID="3352" />
#    <Channel>System</Channel>
#    <Computer>WKS-WIN732BITA.shieldbase.local</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="param1">PsExec</Data>
#    <Data Name="param2">1</Data>
#  </EventData>
#</Event>