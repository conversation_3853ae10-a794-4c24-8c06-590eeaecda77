Author: <PERSON> sa<PERSON><PERSON><EMAIL>
Description: Scheduled Task triggered on logon
EventId: 119
Channel: "Microsoft-Windows-TaskScheduler/Operational"
Provider: Microsoft-Windows-TaskScheduler
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Task: %TaskName%"
    Values:
      -
        Name: TaskName
        Value: "/Event/EventData/Data[@Name=\"TaskName\"]"
  -
    Property: UserName
    PropertyValue: "%UserName%"
    Values:
      -
        Name: UserName
        Value: "/Event/EventData/Data[@Name=\"UserName\"]"
  -
    Property: PayloadData2
    PropertyValue: "Instance Id: %InstanceId%"
    Values:
      -
        Name: InstanceId
        Value: "/Event/EventData/Data[@Name=\"InstanceId\"]"

# Documentation:
# https://kb.eventtracker.com/evtpass/evtpages/EventId_119_Microsoft-Windows-TaskScheduler_61823.asp
# https://mnaoumov.wordpress.com/2014/05/15/task-scheduler-event-ids/
#
# Example Event Data:
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-TaskScheduler" Guid="de7b24ea-73c8-4a09-985d-5bdadcfa9017" />
#     <EventID>119</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>119</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000000000000000</Keywords>
#     <TimeCreated SystemTime="2018-08-19 08:34:30.9399289" />
#     <EventRecordID>182072</EventRecordID>
#     <Correlation ActivityID="f44c625c-8278-4da6-963e-45a59fb2ad2e" />
#     <Execution ProcessID="1484" ThreadID="12476" />
#     <Channel>Microsoft-Windows-TaskScheduler/Operational</Channel>
#     <Computer>base-rd-01.shieldbase.lan</Computer>
#     <Security UserID="S-1-5-18" />
#   </System>
#   <EventData Name="LogonTrigger">
#     <Data Name="TaskName">\Adobe Acrobat Update Task</Data>
#     <Data Name="UserName">shieldbase\tdungan</Data>
#     <Data Name="InstanceId">f44c625c-8278-4da6-963e-45a59fb2ad2e</Data>
#   </EventData>
# </Event>
