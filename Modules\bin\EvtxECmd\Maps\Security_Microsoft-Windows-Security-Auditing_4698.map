Author: <PERSON>
Description: Scheduled Task created
EventId: 4698
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  -
    Property: PayloadData1
    PropertyValue: "TaskName: %TaskName%"
    Values:
      -
        Name: TaskName
        Value: "/Event/EventData/Data[@Name=\"TaskName\"]"
  -
    Property: PayloadData2
    PropertyValue: "TaskContent: %TaskContent%"
    Values:
      -
        Name: TaskContent
        Value: "/Event/EventData/Data[@Name=\"TaskContent\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4698
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4698
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Security-Auditing" Guid="*************-4123-a5ba-3e3z45698c30d" />
#    <EventID>4698</EventID>
#    <Version>0</Version>
#    <Level>0</Level>
#    <Task>12158</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8020042000000000</Keywords>
#    <TimeCreated SystemTime="2020-10-08 19:43:54.1161427" />
#    <EventRecordID>26679060</EventRecordID>
#    <Correlation ActivityID="0b0501cf-9cee-0000-d601-050bab56d601" />
#    <Execution ProcessID="679" ThreadID="12345" />
#    <Channel>Security</Channel>
#    <Computer>HOSTNAZME.DOMAIN.net</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data Name="SubjectUserSid">S-1-5-18</Data>
#    <Data Name="SubjectUserName">HOSTNAME</Data>
#    <Data Name="SubjectDomainName">DOMAIN</Data>
#    <Data Name="SubjectLogonId">0x3E5</Data>
#    <Data Name="TaskName">\Microsoft\Windows\UpdateOrchestrator\AC Power Download</Data>
#    <Data Name="TaskContent">&amp;lt;?xml version="1.0" encoding="UTF-16"?&amp;gt;, &amp;lt;Task version="1.4"&amp;gt;,       &amp;lt;UserId&amp;gt;S-1-5-18&amp;lt;/UserId&amp;gt;,       &amp;lt;RunLevel&amp;gt;LeastPrivilege&amp;lt;/RunLevel&amp;gt;,     &amp;lt;/Principal&amp;gt;,   &amp;lt;/Principals&amp;gt;, &amp;lt;/Task&amp;gt;</Data>
#  </EventData>
# </Event>
