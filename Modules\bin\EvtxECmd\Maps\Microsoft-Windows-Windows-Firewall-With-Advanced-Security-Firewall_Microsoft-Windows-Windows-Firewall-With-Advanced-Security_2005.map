Author: <EMAIL>, <PERSON><PERSON> <PERSON> @hyuunnn
Description: FW rule modified to exception list
EventId: 2005
Channel: Microsoft-Windows-Windows Firewall With Advanced Security/Firewall
Provider: Microsoft-Windows-Windows Firewall With Advanced Security
Maps:
  -
    Property: PayloadData1
    PropertyValue: "%ServiceName%: %RuleName%"
    Values:
      -
        Name: ServiceName
        Value: "/Event/EventData/Data[@Name=\"ServiceName\"]"
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: RemoteHost
    PropertyValue: "%RemoteAddresses%: %RemotePorts%"
    Values:
      -
        Name: RemoteAddresses
        Value: "/Event/EventData/Data[@Name=\"RemoteAddresses\"]"
      -
        Name: RemotePorts
        Value: "/Event/EventData/Data[@Name=\"RemotePorts\"]"
  -
    Property: PayloadData2
    PropertyValue: "%ApplicationPath%"
    Values:
      -
        Name: ApplicationPath
        Value: "/Event/EventData/Data[@Name=\"ApplicationPath\"]"
  -
    Property: PayloadData3
    PropertyValue: "Direction: %Direction%"
    Values:
      -
        Name: Direction
        Value: "/Event/EventData/Data[@Name=\"Direction\"]"
  -
    Property: PayloadData4
    PropertyValue: "Action: %Action%"
    Values:
      -
        Name: Action
        Value: "/Event/EventData/Data[@Name=\"Action\"]"
  -
    Property: PayloadData5
    PropertyValue: "Protocol: %Protocol%"
    Values:
      -
        Name: Protocol
        Value: "/Event/EventData/Data[@Name=\"Protocol\"]"
Lookups:
  -
    Name: Action
    Default: Unknown code
    Values:
      2: Block
      3: Allow
  -
    Name: Protocol
    Default: Unknown code
    Values:
      6: TCP
      17: UDP
      256: All

# Documentation:
# https://kb.eventtracker.com/evtpass/evtPages/EventId_2004_Microsoft-Windows-WindowsFirewallwithAdvancedS_65673.asp
# https://social.technet.microsoft.com/Forums/security/en-US/b1f62441-2c15-4706-b5f6-bb274bfed020/event-id-2004-and-2006-rules-created-automatically-on-windows-firewall?forum=winserversecurity
# https://nasbench.medium.com/finding-forensic-goodness-in-obscure-windows-event-logs-60e978ea45a3
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
#   <System>
#     <Provider Name="Microsoft-Windows-Windows Firewall With Advanced Security" Guid="{GUID}" />
#     <EventID>2005</EventID>
#     <Version>0</Version>
#     <Level>4</Level>
#     <Task>0</Task>
#     <Opcode>0</Opcode>
#     <Keywords>0x8000020000000000</Keywords>
#     <TimeCreated SystemTime="2020-12-12T07:32:38.9121196Z" />
#     <EventRecordID>3509</EventRecordID>
#     <Correlation />
#     <Execution ProcessID="2964" ThreadID="22020" />
#     <Channel>Microsoft-Windows-Windows Firewall With Advanced Security/Firewall</Channel>
#     <Computer>ComputerName</Computer>
#     <Security UserID="{UserID}" />
#   </System>
#   <EventData>
#     <Data Name="RuleId">UDP Query User{~~~~}C:\users\<USER>\downloads\anydesk.exe</Data>
#     <Data Name="RuleName">anydesk.exe</Data>
#     <Data Name="Origin">1</Data>
#     <Data Name="ApplicationPath">C:\users\<USER>\downloads\anydesk.exe</Data>
#     <Data Name="ServiceName" />
#     <Data Name="Direction">1</Data>
#     <Data Name="Protocol">17</Data>
#     <Data Name="LocalPorts">*</Data>
#     <Data Name="RemotePorts">*</Data>
#     <Data Name="Action">3</Data>
#     <Data Name="Profiles">2</Data>
#     <Data Name="LocalAddresses">*</Data>
#     <Data Name="RemoteAddresses">*</Data>
#     <Data Name="RemoteMachineAuthorizationList" />
#     <Data Name="RemoteUserAuthorizationList" />
#     <Data Name="EmbeddedContext" />
#     <Data Name="Flags">257</Data>
#     <Data Name="Active">1</Data>
#     <Data Name="EdgeTraversal">3</Data>
#     <Data Name="LooseSourceMapped">0</Data>
#     <Data Name="SecurityOptions">0</Data>
#     <Data Name="ModifyingUser">S-1-5-21-2856883770-2223847312-2813546713-1001</Data>
#     <Data Name="ModifyingApplication">C:\Windows\System32\dllhost.exe</Data>
#     <Data Name="SchemaVersion">522</Data>
#     <Data Name="RuleStatus">65536</Data>
#     <Data Name="LocalOnlyMapped">0</Data>
#   </EventData>
# </Event>
