Author: <PERSON>
Description: Scheduled Task created
EventId: 129
Channel: "Microsoft-Windows-TaskScheduler/Operational"
Provider: Microsoft-Windows-TaskScheduler
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Task: %TaskName%"
    Values:
      -
        Name: TaskName
        Value: "/Event/EventData/Data[@Name=\"TaskName\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%Path%"
    Values:
      -
        Name: Path
        Value: "/Event/EventData/Data[@Name=\"Path\"]"
  -
    Property: PayloadData2
    PropertyValue: "ProcessID: %ProcessID%"
    Values:
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessID\"]"

# Documentation:
# http://intelligentsystemsmonitoring.com/knowledgebase/windows-operating-system/event-id-task-monitoring-and-control-12716/
# https://www.cyprich.com/2017/03/29/common-task-scheduler-event-ids/
# https://mnaoumov.wordpress.com/2014/05/15/task-scheduler-event-ids/
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-TaskScheduler" Guid="de7b24ea-73c8-4a09-985d-5bdhwcfa9017" />
#    <EventID>129</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>129</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2020-12-04 23:34:48.0504792" />
#    <EventRecordID>298745</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="7436" ThreadID="4792" />
#    <Channel>Microsoft-Windows-TaskScheduler/Operational</Channel>
#    <Computer>hostname.domain.com</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData Name="CreatedTaskProcess">
#    <Data Name="TaskName">\GoogleUpdateTaskMachineUA</Data>
#    <Data Name="Path">C:\Program Files (x86)\Google\Update\GoogleUpdate.exe</Data>
#    <Data Name="ProcessID">1234</Data>
#    <Data Name="Priority">7</Data>
#  </EventData>
# </Event>
