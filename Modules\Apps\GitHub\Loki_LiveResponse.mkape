Description: Loki - Simple IOC and Incident Response Scanner - Live Response
Category: LiveResponse
Author: <PERSON>
Version: 1.0
Id: e93f1052-4d8f-4b63-b680-2af845e95a98
BinaryUrl: https://github.com/Neo23x0/Loki/releases/download/v0.44.2/loki_0.44.2.zip
ExportFormat: log
Processors:
    -
        Executable: Loki\loki.exe
        CommandLine: "-p %sourceDirectory% --logfolder %destinationDirectory% --debug"
        ExportFormat: log

# Documentation
# HOW TO PLACE THE BINARY
# 1. Download loki using the link above. It's a free tool
# 2. Unzip loki.zip into '<KAPE_working_directory>/Modules'
# 3. Update the yara signatures -> 'loki.exe --update'
# 4. KAPE should now be able to find the executable in '<KAPE_working_directory>/Modules/bin/loki/loki.exe'
# 5. Use 'loki.exe --help' for options
# 6. More info here: https://www.nextron-systems.com/loki/
