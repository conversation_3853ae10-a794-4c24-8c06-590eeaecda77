Author: <PERSON><PERSON><PERSON> @lennaert89
Description: OAlerts 300 event
EventId: 300
Channel: OAlerts
Provider: "Microsoft Office 16 Alerts"
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Program: %PayloadData1%"
    Values:
      -
        Name: PayloadData1
        Value: "/Event/EventData/Data"
        Refine: "^(.*)"
  -
    Property: PayloadData2
    PropertyValue: "Alert: %PayloadData2%"
    Values:
      -
        Name: PayloadData2
        Value: "/Event/EventData/Data"
        Refine: "(?<=, )[^,\\d]+(?=,)"

# Documentation:
# https://medium.com/@bromiley/oalerts-the-microsoft-office-event-log-ad164e1eec0f
# https://social.technet.microsoft.com/Forums/ie/en-US/6f684761-12f1-453f-aa37-57ae50828ee7/microsoft-outlook-event-id-300-quotwe-need-to-know-who-to-send-this-to-make-sure-you-enter-at?forum=officeitpro
# https://nasbench.medium.com/finding-forensic-goodness-in-obscure-windows-event-logs-60e978ea45a3
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft Office 16 Alerts" />
#    <EventID Qualifiers="0">300</EventID>
#    <Level>4</Level>
#    <Task>0</Task>
#    <Keywords>0x80000000000000</Keywords>
#    <TimeCreated SystemTime="2019-03-27 13:58:42.5609578" />
#    <EventRecordID>7</EventRecordID>
#    <Channel>OAlerts</Channel>
#    <Computer>FOR-LT03</Computer>
#    <Security />
#  </System>
#  <EventData>
#    <Data>Microsoft Excel
# , Wilt u de wijzigingen in full_application_event_log.csv opslaan?
# , 100216
# , 16.0.4822.1000
# ,
# ,
# </Data>
#    <Binary></Binary>
#  </EventData>
# </Event>
