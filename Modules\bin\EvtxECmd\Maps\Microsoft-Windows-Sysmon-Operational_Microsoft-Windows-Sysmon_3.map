Author: <PERSON>
Description: Network connection
EventId: 3
Channel: Microsoft-Windows-Sysmon/Operational
Provider: Microsoft-Windows-Sysmon
Maps:

  -
    Property: PayloadData1
    PropertyValue: "ProcessID: %ProcessID%, ProcessGUID: %ProcessGUID%"
    Values:
      -
        Name: ProcessGUID
        Value: "/Event/EventData/Data[@Name=\"ProcessGuid\"]"
      -
        Name: ProcessID
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData2
    PropertyValue: "RuleName: %RuleName%"
    Values:
      -
        Name: RuleName
        Value: "/Event/EventData/Data[@Name=\"RuleName\"]"
  -
    Property: PayloadData3
    PropertyValue: "SourceHostname: %SourceHostname%"
    Values:
      -
        Name: SourceHostname
        Value: "/Event/EventData/Data[@Name=\"SourceHostname\"]"
  -
    Property: PayloadData4
    PropertyValue: "SourceIp: %SourceIp%"
    Values:
      -
        Name: SourceIp
        Value: "/Event/EventData/Data[@Name=\"SourceIp\"]"
  -
    Property: PayloadData5
    PropertyValue: "DestinationHostname: %DestinationHostname%"
    Values:
      -
        Name: DestinationHostname
        Value: "/Event/EventData/Data[@Name=\"DestinationHostname\"]"
  -
    Property: PayloadData6
    PropertyValue: "DestinationIp: %DestinationIp%"
    Values:
      -
        Name: DestinationIp
        Value: "/Event/EventData/Data[@Name=\"DestinationIp\"]"
  -
    Property: UserName
    PropertyValue: "%User%"
    Values:
      -
        Name: User
        Value: "/Event/EventData/Data[@Name=\"User\"]"

# Documentation:
# https://docs.microsoft.com/en-us/sysinternals/downloads/sysmon#events
# https://github.com/sbousseaden/EVTX-ATTACK-SAMPLES
# https://www.blackhillsinfosec.com/a-sysmon-event-id-breakdown/
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx - filter on Sysmon
# https://jpcertcc.github.io/ToolAnalysisResultSheet
#
# Example Event Data:
# <Event>
#  <System>
#    <Provider Name="Microsoft-Windows-Sysmon" Guid="5770385f-c22a-43e0-bf4c-06f5698ffbd9" />
#    <EventID>3</EventID>
#    <Version>5</Version>
#    <Level>4</Level>
#    <Task>3</Task>
#    <Opcode>0</Opcode>
#    <Keywords>0x8000000000000000</Keywords>
#    <TimeCreated SystemTime="2019-05-14 17:42:53.8543815" />
#    <EventRecordID>14922</EventRecordID>
#    <Correlation />
#    <Execution ProcessID="1808" ThreadID="2120" />
#    <Channel>Microsoft-Windows-Sysmon/Operational</Channel>
#    <Computer>DC1.insecurebank.local</Computer>
#    <Security UserID="S-1-5-18" />
#  </System>
#  <EventData>
#    <Data Name="RuleName"></Data>
#    <Data Name="UtcTime">2019-05-14 17:42:52.819</Data>
#    <Data Name="ProcessGuid">dfae8213-f1b5-5cda-0000-0010eb030000</Data>
#    <Data Name="ProcessId">4</Data>
#    <Data Name="Image">System</Data>
#    <Data Name="User">NT AUTHORITY\SYSTEM</Data>
#    <Data Name="Protocol">tcp</Data>
#    <Data Name="Initiated">False</Data>
#    <Data Name="SourceIsIpv6">False</Data>
#    <Data Name="SourceIp">**********</Data>
#    <Data Name="SourceHostname">DC1.insecurebank.local</Data>
#    <Data Name="SourcePort">445</Data>
#    <Data Name="SourcePortName">microsoft-ds</Data>
#    <Data Name="DestinationIsIpv6">False</Data>
#    <Data Name="DestinationIp">**********</Data>
#    <Data Name="DestinationHostname">ALICE</Data>
#    <Data Name="DestinationPort">49304</Data>
#    <Data Name="DestinationPortName"></Data>
#  </EventData>
# </Event>
