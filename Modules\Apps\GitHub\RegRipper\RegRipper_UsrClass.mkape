Description: 'RegRipper: parse UsrClass hives'
Category: Registry
Author: <PERSON> (@Karneades)
Version: 1.0
Id: 253acccb-86c0-49dd-9b6d-b9c228a49a55
BinaryUrl: https://github.com/keydet89/RegRipper3.0/archive/master.zip
ExportFormat: txt
FileMask: UsrClass.dat
Processors:
    -
        Executable: regripper\rip.exe
        CommandLine: -r %sourceFile% -f UsrClass
        ExportFormat: txt
        ExportFile: regripper-usrclass.txt

# Documentation
# https://github.com/keydet89/RegRipper3.0
# Create a folder "regripper" within the "Modules\bin" KAPE folder
# Place "rip.exe", "p2x5124.dll" and the "plugins" folder into "Modules\bin\regripper"
