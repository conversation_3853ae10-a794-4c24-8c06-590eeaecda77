Author: <PERSON> sa<PERSON><EMAIL>
Description: Process tracking
EventId: 4688
Channel: Security
Provider: Microsoft-Windows-Security-Auditing
Maps:
  -
    Property: UserName
    PropertyValue: "%domain%\\%user%"
    Values:
      -
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      -
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  -
    Property: PayloadData1
    PropertyValue: "Parent process: %ParentProcessName%"
    Values:
      -
        Name: ParentProcessName
        Value: "/Event/EventData/Data[@Name=\"ParentProcessName\"]"
  -
    Property: PayloadData2
    PropertyValue: "PID: %ProcessId%"
    Values:
      -
        Name: ProcessId
        Value: "/Event/EventData/Data[@Name=\"ProcessId\"]"
  -
    Property: PayloadData3
    PropertyValue: "Mandatory label: %MandatoryLabel%"
    Values:
      -
        Name: MandatoryLabel
        Value: "/Event/EventData/Data[@Name=\"MandatoryLabel\"]"
  -
    Property: ExecutableInfo
    PropertyValue: "%NewProcessName% %CommandLine%"
    Values:
      -
        Name: NewProcessName
        Value: "/Event/EventData/Data[@Name=\"NewProcessName\"]"
      -
        Name: CommandLine
        Value: "/Event/EventData/Data[@Name=\"CommandLine\"]"

# Documentation:
# https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/event.aspx?eventid=4688
# https://docs.microsoft.com/en-us/windows/security/threat-protection/auditing/event-4688
# https://jpcertcc.github.io/ToolAnalysisResultSheet/
#
# Example Event Data:
# <Event xmlns="http://schemas.microsoft.com/win/2004/08/events/event">
# <System>
# <Provider Name="Microsoft-Windows-Security-Auditing" Guid="{*************-4994-A5BA-3E3B0328C30D}" />
# <EventID>4688</EventID>
# <Version>2</Version>
# <Level>0</Level>
# <Task>13312</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8020000000000000</Keywords>
# <TimeCreated SystemTime="2015-11-12T02:24:52.377352500Z" />
# <EventRecordID>2814</EventRecordID>
# <Correlation />
# <Execution ProcessID="4" ThreadID="400" />
# <Channel>Security</Channel>
# <Computer>WIN-GG82ULGC9GO.contoso.local</Computer>
# <Security />
# </System>
# <EventData>
# <Data Name="SubjectUserSid">S-1-5-18</Data>
# <Data Name="SubjectUserName">WIN-GG82ULGC9GO$</Data>
# <Data Name="SubjectDomainName">CONTOSO</Data>
# <Data Name="SubjectLogonId">0x3e7</Data>
# <Data Name="NewProcessId">0x2bc</Data>
# <Data Name="NewProcessName">C:\\Windows\\System32\\rundll32.exe</Data>
# <Data Name="TokenElevationType">%%1938</Data>
# <Data Name="ProcessId">0xe74</Data>
# <Data Name="CommandLine" />
# <Data Name="TargetUserSid">S-1-5-21-1377283216-344919071-3415362939-1104</Data>
# <Data Name="TargetUserName">dadmin</Data>
# <Data Name="TargetDomainName">CONTOSO</Data>
# <Data Name="TargetLogonId">0x4a5af0</Data>
# <Data Name="ParentProcessName">C:\\Windows\\explorer.exe</Data>
# <Data Name="MandatoryLabel">S-1-16-8192</Data>
# </EventData>
# </Event>
