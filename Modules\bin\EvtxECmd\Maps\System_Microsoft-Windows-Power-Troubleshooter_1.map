Author: <PERSON>
Description: Sleep/wake events
EventId: 1
Channel: System
Provider: Microsoft-Windows-Power-Troubleshooter
Maps:
  -
    Property: PayloadData1
    PropertyValue: "Sleep duration: %SleepDuration%"
    Values:
      -
        Name: SleepDuration
        Value: "/Event/EventData/Data[@Name=\"SleepDuration\"]"
  -
    Property: PayloadData2
    PropertyValue: "Wake source: %WakeSourceType%"
    Values:
      -
        Name: WakeSourceType
        Value: "/Event/EventData/Data[@Name=\"WakeSourceType\"]"
  -
    Property: PayloadData3
    PropertyValue: "Wake source text %WakeSourceText%"
    Values:
      -
        Name: WakeSourceText
        Value: "/Event/EventData/Data[@Name=\"WakeSourceText\"]"
Lookups:
  -
    Name: WakeSourceType
    Default: Unknown code
    Values:
      0: Unknown
      1: Power button
      3: Waking from sleep to hibernate
      5: Device (See WakeSourceText for details)
      6: Timer (See WakeSourceText for details)

# Documentation:
# https://www.forensicfocus.com/forums/general/event-id-42-1/
# https://eventlogxp.com/blog/troubleshooting-unplanned-windows-automatic-wake-up/
#
# Example Event Data:
# <Event>
# <System>
# <Provider Name="Microsoft-Windows-Power-Troubleshooter" Guid="cdc05e28-c449-49c6-b9d2-88cf761644df" />
# <EventID>1</EventID>
# <Version>3</Version>
# <Level>4</Level>
# <Task>0</Task>
# <Opcode>0</Opcode>
# <Keywords>0x8000000000000000</Keywords>
# <TimeCreated SystemTime="2020-09-18 03:28:36.5759038" />
# <EventRecordID>2671</EventRecordID>
# <Correlation ActivityID="c87105b8-7de4-4a4c-b194-2a9c9e702069" />
# <Execution ProcessID="5228" ThreadID="11928" />
# <Channel>System</Channel>
# <Computer>win-gist</Computer>
# <Security UserID="S-1-5-19" />
# </System>
# <EventData>
# <Data Name="SleepTime">2020-09-18 03:18:35.0664609</Data>
# <Data Name="WakeTime">2020-09-18 03:28:35.8899669</Data>
# <Data Name="SleepDuration">1029</Data>
# <Data Name="WakeDuration">6389</Data>
# <Data Name="DriverInitDuration">5716</Data>
# <Data Name="BiosInitDuration">1042</Data>
# <Data Name="HiberWriteDuration">0</Data>
# <Data Name="HiberReadDuration">0</Data>
# <Data Name="HiberPagesWritten">0</Data>
# <Data Name="Attributes">1912628224</Data>
# <Data Name="TargetState">4</Data>
# <Data Name="EffectiveState">4</Data>
# <Data Name="WakeSourceType">6</Data>
# <Data Name="WakeSourceTextLength">128</Data>
# <Data Name="WakeSourceText">Windows will execute 'NT TASK\Microsoft\Windows\UpdateOrchestrator\Reboot_AC' scheduled task that requested waking the computer.</Data>
# <Data Name="WakeTimerOwnerLength">52</Data>
# <Data Name="WakeTimerContextLength">18</Data>
# <Data Name="NoMultiStageResumeReason">0</Data>
# <Data Name="WakeTimerOwner">\Device\HarddiskVolume3\Windows\System32\svchost.exe</Data>
# <Data Name="WakeTimerContext">SystemEventsBroker</Data>
# <Data Name="CheckpointDuration">98</Data>
# </EventData>
# </Event>
