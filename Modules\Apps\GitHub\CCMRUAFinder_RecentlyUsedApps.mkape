Description: 'Extracts SCCM software metering RecentlyUsedApplication logs from OBJECTS.DATA files'
Category: ProgramExecution
Author: <PERSON>
Version: 1.0
Id: 591b2715-f4eb-47bd-9a6c-249b8c22aba1
BinaryUrl: https://github.com/esecrpm/WMI_Forensics/raw/master/CCM_RUA_Finder.exe
ExportFormat: tsv
FileMask: OBJECTS.DATA
Processors:
    -
        Executable: CCM_RUA_Finder.exe
        CommandLine: -i %sourceFile% -o %destinationDirectory%\SCCM_RecentlyUsedApplication.tsv
        ExportFormat: tsv

# Documentation
# https://github.com/davidpany/WMI_Forensics/blob/master/CCM_RUA_Finder.py
# Uses <PERSON>'s CCM_RUA_finder.py to extract SCCM software metering RecentlyUsedApplication logs from OBJECTS.DATA files.
