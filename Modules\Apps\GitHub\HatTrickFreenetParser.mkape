Description: 'Hat-Trick: Freenet Parser'
Category: Misc
Author: <PERSON>
Version: 1.0
Id: 5c546be1-27f9-4739-86f6-e0391d1cc7db
BinaryUrl: https://github.com/northloopforensics/Hat-Trick-Freenet-Parser/releases
ExportFormat: txt
Processors:
    -
        Executable: Hat-Trick.Freenet.Parser.exe
        CommandLine: "%sourceDirectory% %destinationDirectory%"
        ExportFormat: txt

# Documentation
# https://github.com/northloopforensics/Hat-Trick-Freenet-Parser
# Executable to parse Freenet installations using KAPE
# This program was written for use on Kroll's KAPE tool. Download the release and then copy the #executable to: kape\Modules\bin
# Also available in this repository are the Target profile (tkape) file and Module profile #(mkape) file. Copy these to the appropriate locations: kape\Targets\P2P\freenet.tkape & #kape\Modules\Misc\hat-trick-freenet-parser.mkape
# The tool parses: The Location ID and Network Addresses of the Target System Downloaded and #Uploaded File Information Peer IDs, Peer IP Addresses, and Peer-of-Peer Location IDs
