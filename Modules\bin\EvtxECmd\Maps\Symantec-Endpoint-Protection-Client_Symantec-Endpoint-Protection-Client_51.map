Author: <PERSON><PERSON> @gazambelli
Description: Security Risk Found
EventId: 51
Channel: "Symantec Endpoint Protection Client"
Provider: "Symantec Endpoint Protection Client"
Maps:
  -
    Property: ExecutableInfo
    PropertyValue: "%ExecutableInfo%"
    Values:
      -
        Name: ExecutableInfo
        Value: "/Event/EventData/Data"
        Refine: "(?<=File: ).*(?= by: )"
  -
    Property: PayloadData1
    PropertyValue: "Risk: %PayloadData1%"
    Values:
      -
        Name: PayloadData1
        Value: "/Event/EventData/Data"
        Refine: "(?<=Security Risk Found! ).*(?= in File:)"
  -
    Property: PayloadData2
    PropertyValue: "%PayloadData2%"
    Values:
      -
        Name: PayloadData2
        Value: "/Event/EventData/Data"
        Refine: "Action:.*(?= Action Description: )"
  -
    Property: PayloadData3
    PropertyValue: "%PayloadData3%"
    Values:
      -
        Name: PayloadData3
        Value: "/Event/EventData/Data"
        Refine: "Action Description:.*"

# Documentation:
# https://www.eventid.net/display-eventid-51-source-Symantec%20AntiVirus-eventno-9552-phase-1.htm
# https://knowledge.broadcom.com/external/article/156288/symantec-endpoint-protection-121x-event.html
#
# Example Event Data:
# <Event>
# <System>
# <Provider Name="Symantec Endpoint Protection Client" />
# <EventID Qualifiers="16639">51</EventID>
# <Level>2</Level>
# <Task>0</Task>
# <Keywords>0x80000000000000</Keywords>
# <TimeCreated SystemTime="2020-08-20 10:09:01.0000000" />
# <EventRecordID>57000</EventRecordID>
# <Channel>Symantec Endpoint Protection Client</Channel>
# <Computer>hostname.domain</Computer>
# <Security />
# </System>
# <EventData>
# <Data>
# Security Risk Found! Hacktool.XYZ in File: C:\Users\<USER>\Downloads\file.exe by: Auto-Protect scan.  Action: Delete failed : Quarantine failed : Access denied.  Action Description: The process was terminated successfully.</Data>
# <Binary></Binary>
# </EventData>
# </Event>
