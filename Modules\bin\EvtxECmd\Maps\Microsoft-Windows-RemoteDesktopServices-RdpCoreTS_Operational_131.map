Author: <PERSON> sa<PERSON><EMAIL>
Description: RDP server accepted a new TCP connection
EventId: 131
Channel: Microsoft-Windows-RemoteDesktopServices-RdpCoreTS/Operational
Maps: 
  - 
    Property: RemoteHost
    PropertyValue: "%Address%"
    Values: 
      - 
        Name: Address
        Value: "/Event/EventData/Data[@Name=\"ClientIP\"]"
  - 
    Property: PayloadData1
    PropertyValue: "Connection Type: %ConnType%"
    Values: 
      - 
        Name: ConnType
        Value: "/Event/EventData/Data[@Name=\"ConnType\"]"
 
# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6


#The server accepted a new TCP connection from client 174.x.x.x:30022.
#</Event>
#<Event>
#  <System>
#    <Provider Name="Microsoft-Windows-RemoteDesktopServices-RdpCoreTS" Guid="1139c61b-b549-4251-8ed3-27250a1edec8" />
#    <EventID>131</EventID>
#    <Version>0</Version>
#    <Level>4</Level>
#    <Task>4</Task>
#    <Opcode>15</Opcode>
#    <Keywords>0x4000000000000000</Keywords>
#    <TimeCreated SystemTime="2018-07-23 16:24:06.0572055" />
#    <EventRecordID>1743</EventRecordID>
#    <Correlation ActivityID="a3f1f58e-1434-4c4a-9018-54513ab80000" />
#    <Execution ProcessID="464" ThreadID="1848" />
#    <Channel>Microsoft-Windows-RemoteDesktopServices-RdpCoreTS/Operational</Channel>
#    <Computer>base-rd-01.shieldbase.lan</Computer>
#    <Security UserID="S-1-5-20" />
#  </System>
#  <EventData>
#    <Data Name="ConnType">UDP</Data>
#    <Data Name="ClientIP">[*************]:65464</Data>
#  </EventData>
#</Event>