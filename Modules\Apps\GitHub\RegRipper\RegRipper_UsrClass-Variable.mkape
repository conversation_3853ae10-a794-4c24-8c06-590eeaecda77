Description: 'RegRipper: parse UsrClass hives using provided plugin name'
Category: Registry
Author: <PERSON> (@Karneades)
Version: 1.0
Id: 26a1534a-c11d-4434-96c5-864699513aee
BinaryUrl: https://github.com/keydet89/RegRipper3.0/archive/master.zip
ExportFormat: txt
FileMask: UsrClass.dat
Processors:
    -
        Executable: regripper\rip.exe
        CommandLine: -r %sourceFile% -p %usrclassPlugin%
        ExportFormat: txt
        ExportFile: regripper-usrclass-%usrclassPlugin%.txt
        Append: true

# Documentation
# https://github.com/keydet89/RegRipper3.0
# Create a folder "regripper" within the "Modules\bin" KAPE folder
# Place "rip.exe", "p2x5124.dll" and the "plugins" folder into "Modules\bin\regripper"
# Provide a module variable using --mvars usrclassPlugin:pluginname
