Description: 'RegRipper: parse NTUSER hives'
Category: Registry
Author: Ze<PERSON><PERSON>ch <https://{github,twitter}.com/ZeArioch>, <PERSON><PERSON>
Version: 1.1
Id: ce3bf979-0d4d-4a31-8240-6be6f95610b7
BinaryUrl: https://github.com/keydet89/RegRipper3.0/archive/master.zip
ExportFormat: txt
FileMask: ntuser.dat
Processors:
    -
        Executable: regripper\rip.exe
        CommandLine: -r %sourceFile% -f ntuser
        ExportFormat: txt
        ExportFile: regripper-ntuser.txt

# Documentation
# https://github.com/keydet89/RegRipper3.0
# Create a folder "regripper" within the "Modules\bin" KAPE folder
# Place "rip.exe", "p2x5124.dll" and the "plugins" folder into "Modules\bin\regripper"
