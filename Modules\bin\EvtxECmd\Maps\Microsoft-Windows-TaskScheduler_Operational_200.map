Author: <PERSON> sa<PERSON><EMAIL>
Description: Scheduled task executed
EventId: 200
Channel: "Microsoft-Windows-TaskScheduler/Operational"
Maps: 
  - 
    Property: PayloadData1
    PropertyValue: Task %TaskName%
    Values: 
      - 
        Name: TaskName
        Value: "/Event/EventData/Data[@Name=\"TaskName\"]"
  - 
    Property: ExecutableInfo
    PropertyValue: "%ActionName%"
    Values: 
      - 
        Name: ActionName
        Value: "/Event/EventData/Data[@Name=\"ActionName\"]"
  -
    Property: PayloadData3
    PropertyValue: Instance Id %TaskInstanceId%
    Values: 
      - 
        Name: TaskInstanceId
        Value: "/Event/EventData/Data[@Name=\"TaskInstanceId\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
# <Event>
#   <System>
#     <Provider Name="Microsoft-Windows-TaskScheduler" Guid="de7b24ea-73c8-4a09-985d-5bdadcfa9017" />
#     <EventID>200</EventID>
#     <Version>1</Version>
#     <Level>4</Level>
#     <Task>200</Task>
#     <Opcode>1</Opcode>
#     <Keywords>0x8000000000000000</Keywords>
#     <TimeCreated SystemTime="2018-08-19 06:34:00.6419359" />
#     <EventRecordID>181931</EventRecordID>
#     <Correlation ActivityID="b4d1e103-abb2-4443-8640-7d7ed3692e7a" />
#     <Execution ProcessID="1484" ThreadID="13652" />
#     <Channel>Microsoft-Windows-TaskScheduler/Operational</Channel>
#     <Computer>base-rd-01.shieldbase.lan</Computer>
#     <Security UserID="S-1-5-18" />
#   </System>
#   <EventData Name="ActionStart">
#     <Data Name="TaskName">\{acd3ec78-8a26-430e-a368-f2941d85d3f8}</Data>
#     <Data Name="ActionName">Global\JD_TaskSchedulerSchedule_{9300FF5C-FBAB-4A99-8C99-7A6EE055DFDC}</Data>
#     <Data Name="TaskInstanceId">b4d1e103-abb2-4443-8640-7d7ed3692e7a</Data>
#     <Data Name="EnginePID">0</Data>
#   </EventData>
# </Event>
