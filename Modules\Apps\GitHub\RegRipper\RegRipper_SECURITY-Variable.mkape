Description: 'RegRipper: parse SECURITY hive using provided plugin name'
Category: Registry
Author: <PERSON> (@Karneades)
Version: 1.0
Id: 8743b688-bebf-4ae5-8700-aed6681fc4eb
BinaryUrl: https://github.com/keydet89/RegRipper3.0/archive/master.zip
ExportFormat: txt
FileMask: SECURITY
Processors:
    -
        Executable: regripper\rip.exe
        CommandLine: -r %sourceFile% -p %securityPlugin%
        ExportFormat: txt
        ExportFile: regripper-security-%securityPlugin%.txt
        Append: true

# Documentation
# https://github.com/keydet89/RegRipper3.0
# Create a folder "regripper" within the "Modules\bin" KAPE folder
# Place "rip.exe", "p2x5124.dll" and the "plugins" folder into "Modules\bin\regripper"
# Provide a module variable called using --mvars securityPlugin:pluginname
