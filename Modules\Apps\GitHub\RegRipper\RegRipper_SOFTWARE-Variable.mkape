Description: 'RegRipper: parse SOFTWARE hive using provided plugin name'
Category: Registry
Author: <PERSON> (@Karneades)
Version: 1.0
Id: c2e63e6f-4207-4bf4-b6af-d4a465009418
BinaryUrl: https://github.com/keydet89/RegRipper3.0/archive/master.zip
ExportFormat: txt
FileMask: SOFTWARE
Processors:
    -
        Executable: regripper\rip.exe
        CommandLine: -r %sourceFile% -p %softwarePlugin%
        ExportFormat: txt
        ExportFile: regripper-software-%softwarePlugin%.txt
        Append: true

# Documentation
# https://github.com/keydet89/RegRipper3.0
# Create a folder "regripper" within the "Modules\bin" KAPE folder
# Place "rip.exe", "p2x5124.dll" and the "plugins" folder into "Modules\bin\regripper"
# Provide a module variable using --mvars softwarePlugin:pluginname
