Author: <PERSON> sa<PERSON><EMAIL>
Description: Share access
EventId: 5140
Channel: Security
Maps: 
  - 
    Property: Username
    PropertyValue: "%domain%\\%user%"
    Values: 
      - 
        Name: domain
        Value: "/Event/EventData/Data[@Name=\"SubjectDomainName\"]"
      - 
        Name: user
        Value: "/Event/EventData/Data[@Name=\"SubjectUserName\"]"
  - 
    Property: RemoteHost
    PropertyValue: "%ipAddress%:%port%"
    Values: 
      - 
        Name: ipAddress
        Value: "/Event/EventData/Data[@Name=\"IpAddress\"]"
      - 
        Name: port
        Value: "/Event/EventData/Data[@Name=\"IpPort\"]"
  - 
    Property: PayloadData1
    PropertyValue: Share %ShareName% (%ShareLocalPath%)
    Values: 
      - 
        Name: ShareName
        Value: "/Event/EventData/Data[@Name=\"ShareName\"]"
      - 
        Name: ShareLocalPath
        Value: "/Event/EventData/Data[@Name=\"ShareLocalPath\"]"
  - 
    Property: PayloadData2
    PropertyValue: Sid %SubjectUserSid%
    Values: 
      - 
        Name: SubjectUserSid
        Value: "/Event/EventData/Data[@Name=\"SubjectUserSid\"]"

# Valid properties include:
# UserName
# RemoteHost
# ExecutableInfo --> used for things like process command line, scheduled task, info from service install, etc.
# PayloadData1 through PayloadData6

# Example payload data
#   <EventData>
#     <Data Name="SubjectUserSid">S-1-5-21-100689374-1717798114-2601648136-1001</Data>
#     <Data Name="SubjectUserName">SRL-Helpdesk</Data>
#     <Data Name="SubjectDomainName">WKS-WIN732BITA</Data>
#     <Data Name="SubjectLogonId">0x174A094B</Data>
#     <Data Name="ObjectType">File</Data>
#     <Data Name="IpAddress">*********</Data>
#     <Data Name="IpPort">4508</Data>
#     <Data Name="ShareName">\\*\ADMIN$</Data>
#     <Data Name="ShareLocalPath">\??\C:\Windows</Data>
#     <Data Name="AccessMask">0x1</Data>
#     <Data Name="AccessList">%%4416</Data>
#   </EventData>